<template>
  <div>
    <el-form :model="form">
      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">标题样式</el-text>
        </el-col>
        <el-col>
          <el-input
            v-model="form.itemStyle.titleStyle"
            style="width: 240px"
            autosize
            type="textarea"
            placeholder="请输入css样式"
          />
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">列表样式</el-text>
        </el-col>
        <el-col>
          <el-input
            v-model="form.itemStyle.spanStyle"
            style="width: 240px"
            autosize
            type="textarea"
            placeholder="请输入css样式"
          />
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">模块样式</el-text>
        </el-col>
        <el-col>
          <el-input
            v-model="form.itemStyle.modelStyle"
            style="width: 240px"
            autosize
            type="textarea"
            placeholder="请输入css样式"
          />
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import emitter from '@/utils/mitt'

defineOptions({
  name: 'ServiceMenuItemFrom'
})

const props = defineProps<{ itemJson: any }>()


const form = ref({
  titleList: "子系统菜单",
  itemStyle: {
    modelStyle: '',
    titleStyle: '',
    spanStyle: ''
  },
})

defineExpose({ form })

// 状态监听，暴露给父类
watch(
  form,
  () => {
    emitter.emit('ServiceMenuItemFrom', form.value)
  },
  { immediate: true, deep: true }
)
onMounted(() => {
  if (props.itemJson) {
    form.value = props.itemJson
  }
})
</script>

<style lang="scss" scoped>
.el-col {
  margin-bottom: 10px;
}
</style>
