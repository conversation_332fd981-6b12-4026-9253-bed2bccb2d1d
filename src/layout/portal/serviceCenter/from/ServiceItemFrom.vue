<template>
  <div>
    <el-form :model="form">
      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">标题选择</el-text>
        </el-col>
        <el-col :span="18">
          <el-select
            v-model="value"
            clearable
            placeholder="Select"
            style="width: 240px"
            @change="workChange"
          >
            <el-option v-for="item in option" :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">标题样式</el-text>
        </el-col>
        <el-col>
          <el-input
            v-model="form.itemStyle.titleStyle"
            style="width: 240px"
            autosize
            type="textarea"
            placeholder="请输入css样式"
          />
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">列表样式</el-text>
        </el-col>
        <el-col>
          <el-input
            v-model="form.itemStyle.spanStyle"
            style="width: 240px"
            autosize
            type="textarea"
            placeholder="请输入css样式"
          />
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">模块样式</el-text>
        </el-col>
        <el-col>
          <el-input
            v-model="form.itemStyle.modelStyle"
            style="width: 240px"
            autosize
            type="textarea"
            placeholder="请输入css样式"
          />
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import emitter from '@/utils/mitt'

defineOptions({
  name: 'ServiceItemFrom'
})

const props = defineProps<{ itemJson: any }>()

const option = ref([
  { id: '1', title: '党建工团' },
  { id: '2', title: '行政办公' },
  { id: '3', title: '三会决策' },
  { id: '4', title: '事项督办' },
  { id: '5', title: '企业发展' },
  { id: '6', title: '人力资源' },
  { id: '7', title: '市场经营' },
  { id: '8', title: '设计履约' },
  { id: '9', title: '招标采购' },
  { id: '10', title: '财务管理' },
  { id: '11', title: '投资运营' },
  { id: '12', title: '纪监审法' },
  { id: '13', title: '外事管理' },
  { id: '14', title: '用车管理' },
  { id: '15', title: '信息化' },
  { id: '16', title: '科技管理' },
  { id: '17', title: '技术质量' },
  { id: '18', title: '档案管理' }
])

const value = ref<string>('1')

const form = ref({
  titleList: option.value[0],
  itemStyle: {
    modelStyle: '',
    titleStyle: '',
    spanStyle: ''
  },
  isShow: {
    showMywork: true,
    showRanKing: true
  }
})

defineExpose({ form })

// 状态监听，暴露给父类
watch(
  form,
  () => {
    emitter.emit('serviceItemFrom', form.value)
  },
  { immediate: true, deep: true }
)
const workChange = () => {
  let data = option.value.find((item) => item.id == value.value)
  if (data) {
    form.value.titleList = data
  }
}
onMounted(() => {
  if (props.itemJson) {
    form.value = props.itemJson
  }
  setValue()
})
const setValue = () => {
  value.value = form.value.titleList.id
}
</script>

<style lang="scss" scoped>
.el-col {
  margin-bottom: 10px;
}
</style>
