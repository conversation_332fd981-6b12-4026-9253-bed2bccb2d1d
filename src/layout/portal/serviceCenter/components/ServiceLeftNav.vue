<template>
  <div class="uk-width-1-6 custom-left-menu-bar" :style="itemStyle.modelStyle">
    <div class="uk-card uk-card-default uk-card-small uk-card-body" style="min-height: 350px">
      <div class="custom-left-navigation-title">
        <img src="@/assets/imgs/lefttitle.png" /><span :style="itemStyle.titleStyle"
          >子系统入口</span
        >
      </div>

      <ul
        class="custom-left-menu-list"
        id="custom-left-menu-list"
        @mouseenter="handleMouseEnter"
        @mouseleave="handleMouseLeave"
      >
        <li
          v-for="(item, index) in leftMenuCategory"
          :key="index"
          :class="[getItemClass(item), { active: activeCode === item.code }]"
          @mouseenter="onEnter(index)"
        >
          <div>
            <div
              :class="['left-menu-name', { active: activeCode === item.code }]"
              :style="itemStyle.spanStyle"
              >{{ item.name }}</div
            >
            <el-icon :size="10" style="left: 0%">
              <ArrowRight />
            </el-icon>
          </div>
        </li>
        <div
          v-show="isContentVisible"
          id="custom-right-menu-list"
          class="custom-left-menu-entry-content-itme"
          :class="{ show: isContentVisible }"
          @mouseenter="onEnterContent"
          @mouseleave="onLeaveContent"
        >
          <div class="menu_list">
            <div class="group" v-for="(menuGroup, index) in menuListGroups" :key="index">
              <a
                v-for="subItem in filterSubItems(menuGroup)"
                :key="subItem.XTFL"
                target="_blank"
                :href="go_url(subItem)"
                :style="itemStyle.spanStyle"
                >{{ subItem.ShowName }}</a
              >
            </div>
          </div>
        </div>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useUserStore } from '@/store/modules/user'
import * as PortalApi from '@/api/system/portal'
import { getAccessToken } from '@/utils/auth'
import $ from 'jquery'
import emitter from '@/utils/mitt'
import { ArrowRight } from '@element-plus/icons-vue'
import { replaceUrl } from '@/assets/js/NK'
import { getFullUrl } from '@/layout/portal/admin'

interface itemType {
  titleList: {
    id: ''
    title: ''
  }
  itemStyle: {
    modelStyle: ''
    titleStyle: ''
    spanStyle: ''
  }
  isShow: {}
}
defineOptions({
  name: 'ServiceLeftNav'
})

const leftMenuCategory = ref()
const menu_list = ref([])
const activeCode = ref(null)
const isContentVisible = ref(false)
const leftMenuShowColumnNumber = ref(4)

const getItemClass = (item) => `custom-left-menu-item-${item.code}`

const props = defineProps<{ itemJson: itemType; itemId: number }>()

const onEnter = (index) => {
  $('.custom-left-menu-list li div span').css('visibility', 'hidden')
  isContentVisible.value = true
  activeCode.value = leftMenuCategory.value[index].code
}

const onLeave = () => {
  $('.custom-left-menu-list li div span').css('visibility', 'visible')
  isContentVisible.value = false
  activeCode.value = null
}

const onEnterContent = () => {
  $('.custom-left-menu-list li div span').css('visibility', 'hidden')
  isContentVisible.value = true
}

const onLeaveContent = () => {
  $('.custom-left-menu-list li div span').css('visibility', 'visible')
  isContentVisible.value = false
}

const go_url = (subItem) => {
  let url = subItem.Url

  if (url.indexOf('?') === -1) {
    url += '?'
  } else {
    url += '&'
  }
  url += 'code=' + subItem.DoorCode
  if (url.indexOf('token') === -1) {
    url += '&token=' + subItem.Token
  }
  url = replaceUrl({ url: url })
  // 相对路径修改为绝对路径
  if (!url.startsWith('http')) {
    url = getFullUrl(url)
  }
  return url
}

const group = (array, groupLength) => {
  let index = 0
  let newArray = []
  while (index < array.length) {
    newArray.push(array.slice(index, (index += groupLength)))
  }
  return newArray
}

const loadLeftMenu = async () => {
  try {
    const userStore = useUserStore()
    const sqlData = await PortalApi.execSystemScript({
      code: 'SQL_adde0127f04a415780e9df7d0316867d',
      ExecData: JSON.stringify([{ UserID: userStore.getUser.id }])
    })

    const temp_list = sqlData.map((item) => ({
      Url: item.Url,
      DoorCode: item.DoorCode,
      Token: getAccessToken(),
      ShowName: item.ShowName,
      XTFL: item.XTFL
    }))

    menu_list.value = group(temp_list, leftMenuShowColumnNumber.value)

    const result = await PortalApi.serviceCenterGetLeftListCat()
    const _temp = new Set()
    leftMenuCategory.value = result
      .filter((item) => {
        const found = temp_list.some((item2) => item.Code === item2.XTFL && !_temp.has(item.Code))
        if (found) _temp.add(item.Code)
        return found
      })
      .map((item) => ({ code: item.Code, name: item.Name }))
  } catch (error) {
    console.error('Error loading left menu:', error)
  }
}

const itemStyle = ref({
  modelStyle: {},
  titleStyle: {},
  spanStyle: {}
})
const isShow = ref()

const titleItem = ref({ id: '', title: '' })

emitter.on('ServiceLeftNavFrom', (obj: any) => {
  init(obj)
})
const init = (itemJson: itemType) => {
  if (itemJson) {
    titleItem.value = itemJson.titleList
    itemStyle.value = itemJson.itemStyle
    isShow.value = itemJson.isShow
  }
}

// 服务中心数据处理
onMounted(() => {
  loadLeftMenu()
  init(props.itemJson)
})

// 移入
const handleMouseEnter = () => {
  emitter.emit('navWidthChange', { width: 1190, itemId: props.itemId })
}
// 移出
const handleMouseLeave = () => {
  const width = document.getElementById('custom-left-menu-list')
  isContentVisible.value = false
  emitter.emit('navWidthChange', { width: width?.offsetWidth, itemId: props.itemId })
}

const menuListGroups = computed(() => group(menu_list.value.flat(), leftMenuShowColumnNumber.value))

const filterSubItems = (menuGroup) => {
  return menuGroup.filter((subItem) => activeCode.value === subItem.XTFL)
}
</script>

<style scoped>
@import url('@/assets/css/uk.min.css');
@import url('@/assets/css/master.css');
@import url('@/assets/css/bootstrap.min.css');

.uk-card-default {
  background-color: transparent;
  box-shadow: none;
}
.uk-section-default {
  margin: 0 0 150px 0;
  background: #f9fafd;
}

.custom-left-navigation-title {
  margin-bottom: 2px;
}

.custom-left-navigation-title img {
  width: 30px;
  height: 20px;
  padding-right: 10px;
  margin-bottom: 6px;
}

.custom-left-navigation-title span {
  font-size: 16px;
  color: #333;
}

.custom-left-menu-list {
  padding: 0;
  width: 170px;
}
.custom-left-menu-list li div {
  padding: 5px 0;
  text-align: right;
}

.custom-left-menu-list li div div {
  color: #666;
  font-size: 14px;
  display: inline-block;
  padding-right: 80px;
  cursor: pointer;
}

.custom-left-menu-list li div span {
  display: inline-block;
  padding-right: 10px;
  width: 20px;
}

.custom-left-menu-list li {
  border-left: 4px solid white;
  /* margin-left: -30px; */
}
.custom-left-menu-list li:hover {
  color: #0070ff;
  cursor: pointer;
  border-left: 4px solid #0070ff;
  background: #f4faff;
}

.custom-left-menu-entry-content-itme {
  position: absolute;
  left: 170px;
  top: 0;
  min-width: 1190px;
  min-height: 350px;
  border: 1px solid #c6ddfb;
  -webkit-transition: top 0.25s ease;
  transition: top 0.25s ease;
  background-color: #fff;
}

/**左侧显示菜单项*/
.custom-left-menu-bar {
  width: 240px;
  position: fixed;
}

.custom-right-menu-content {
  position: relative;
  left: 230px;
  margin-bottom: 100px;
}

.custom-left-menu-entry-content-itme {
  padding: 20px;
}
a {
  text-decoration: none;
}
a:hover {
  text-decoration: none;
}

.menu_list a {
  display: block;
  height: 36px;
  color: #333;
  padding: 10px;
  width: 25%;
  border-left: 1px solid rgb(229, 229, 229);
  float: Left;
  font-size: 14px;
  padding-left: 20px;
}
.menu_list a:before {
  content: '';
  position: absolute;
  width: 1.5em;
  height: 1.5em;
  background-image: url(data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%226%22%20height%3D%226%22%20viewBox%3D%220%200%206%206%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%20%20%20%20%3Ccircle%20fill%3D%22%23666%22%20cx%3D%223%22%20cy%3D%223%22%20r%3D%223%22%20%2F%3E%0A%3C%2Fsvg%3E);
  background-repeat: no-repeat;
  background-position: 50% 50%;
  margin-left: -20px;
}
</style>
