<template>
  <div class="uk-flex work-center" :style="itemStyle.modelStyle">
    <div class="uk-card uk-card-default work-card">
      <div class="uk-flex uk-card-header">
        <div
          class="head-title-list"
          @click="loadTask(title.id, title.sign)"
          v-for="title in titleList"
          :key="title.id"
          :class="nowWork == title.sign ? 'active' : ''"
        >
          <span :class="nowWork == title.sign ? title.sign : ''"></span>
          <span :class="nowWork == title.sign ? 'active uk-card-title' : 'uk-card-title'">{{
            title.title
          }}</span>
          <span
            :class="nowWork == title.sign ? 'active uk-badge' : 'uk-badge'"
            v-if="title.count > 0 && isShow.showCount"
            v-text="title.count"
          ></span>
        </div>
        <a @click="tabMore(0, $event)"><img src="@/assets/icons/portal/icon-more.png" /></a>
      </div>

      <div class="uk-card-body">
        <template v-if="dataCount > 0 && is4Alogin">
          <ul class="uk-list">
            <template v-if="dataType === 'newtask'">
              <template v-for="(item, index) in dataList" :key="index">
                <li class="uk-flex" @click="OpenView(2, item)">
                  <div class="icon">
                    <img
                      title="取消关注"
                      v-if="item.IsFocus == 'true'"
                      @click.stop="FocusFlow(0, item, dataType)"
                      src="@/assets/icons/portal/icon-tuding-copy.png"
                    />
                    <img
                      title="关注任务"
                      v-else-if="item.IsFocus == 'false'"
                      @click.stop="FocusFlow(1, item, dataType)"
                      src="@/assets/icons/portal/icon-tudingwb.png"
                    />
                  </div>

                  <div class="title" :title="item.ActivityName" :style="{ color: item.ExtsField }">
                    {{ item.ActivityName }}</div
                  >
                  <div class="cate">
                    <span v-if="item.GoodWaySoft === 'RIIT'">信息院综合管理系统</span>
                    <span v-else-if="item.GoodWaySoft === 'PC_SRMSFlow'">科技系统</span>
                    <span v-else-if="item.GoodWaySoft === 'JiuQiCaiWu'">财务系统</span>
                    <span v-else-if="item.GoodWaySoft === 'PC_PRPFlow'">EPC系统</span>
                    <span v-else :title="item.FlowName">{{ item.FlowName }}</span>
                  </div>
                  <div class="info" :style="itemStyle.listStyle">
                    <span class="dept" :title="item.FromDeptName" v-if="item.FromDeptName"
                      >{{ item.ShortDeptName }}：</span
                    >
                    <span class="dept" v-else></span>
                    <span class="name" v-if="item.FromUserNames">{{ item.FromUserNames }}</span>
                    <span class="name" v-else></span>
                    <span
                      class="time formate"
                      v-text="formatDate(item.CreateTime, itemStyle.dateFormat)"
                    ></span>
                  </div>
                </li>
              </template>
            </template>

            <template v-else-if="dataType === 'completetask'">
              <template v-for="(item, index) in dataList" :key="index">
                <li class="uk-flex" @click="OpenView(2, item)">
                  <div class="icon">
                    <img
                      title="取消关注"
                      v-if="item.IsFocus == 'true'"
                      @click.stop="FocusFlow(0, item, dataType)"
                      src="@/assets/icons/portal/icon-tuding-copy.png"
                    />
                    <img
                      title="关注任务"
                      v-else-if="item.IsFocus == 'false'"
                      @click.stop="FocusFlow(1, item, dataType)"
                      src="@/assets/icons/portal/icon-tudingwb.png"
                    />
                  </div>

                  <div class="title" :title="item.TaskName">{{ item.TaskName }} </div>
                  <div class="cate">
                    <span v-if="item.GoodWaySoft === 'RIIT'">信息院综合管理系统</span>
                    <span v-else-if="item.GoodWaySoft === 'PC_SRMSFlow'">科技系统</span>
                    <span v-else-if="item.GoodWaySoft === 'JiuQiCaiWu'">财务系统</span>
                    <span v-else-if="item.GoodWaySoft === 'PC_PRPFlow'">EPC系统</span>
                    <span v-else :title="item.FlowName">{{ item.FlowName }}</span>
                  </div>
                  <div class="info">
                    <span class="dept" v-if="item.CreateDeptName" :title="item.CreateDeptName">
                      {{ item.CreateUserDeptName }}：</span
                    >
                    <span class="name">{{ item.CreateUserName }}</span>
                    <!-- <span class='time'>{{ item.ExecTime.substring(0, 16) }}</span> -->
                  </div>
                </li>
              </template>
            </template>

            <template v-else-if="dataType === 'focus'">
              <template v-for="(item, index) in dataList" :key="index">
                <li class="uk-flex" @click="OpenView(2, item)" :style="itemStyle.listStyle">
                  <div class="icon">
                    <img
                      @click.stop="FocusFlow(0, item, dataType)"
                      src="@/assets/icons/portal/icon-tuding-copy.png"
                    />
                  </div>

                  <div class="title" :title="item.FlowName">{{ item.FlowName }}</div>

                  <div class="apply-flow info">
                    <div class="dept">
                      <span>审批状态：</span>
                      <span :title="item.FlowState">{{ item.FlowState }}</span>
                    </div>
                    <div class="name">
                      <span>关注日期：</span>
                      <span v-if="item && item.CreateTime">{{
                        formatDate(item.CreateTime, itemStyle.dateFormat)
                      }}</span>
                    </div>
                    <div class="time">
                      <span>当前审批步骤：</span>
                      <span :title="item.CurrentStep">{{ item.CurrentStep }}</span>
                    </div>
                  </div>
                </li>
              </template>
            </template>
          </ul>
        </template>

        <div class="emptybox" v-if="show_loading || !is4Alogin" style="margin-top: 5%">
          <div>
            <span class="line-scale loading_icon">
              <div></div>
              <div></div>
              <div></div>
              <div></div>
              <div></div>
            </span>
          </div>
        </div>
        <template v-if="dataCount <= 0 && !show_loading && is4Alogin">
          <div class="empty-img">
            <img src="@/assets/imgs/nodata.png" />
            <div>暂无数据</div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as PortalApi from '@/api/system/portal'
import * as portal from '@/api/portal'
import { messageInfo } from '@/utils/websocket'
import { formatDate } from '@/utils/formatTime'
import emitter from '@/utils/mitt'
import { OpenView, tabMore } from '@/layout/portal/admin'
import { useCache, CACHE_KEY } from '@/hooks/web/useCache'
import Cookies from "js-cookie";
const { wsCache } = useCache()
defineOptions({
  name: 'WorkList'
})

//url为自己的路径
const message = useMessage()
const is4Alogin = ref(false)
const nowWork = ref('mewtask')

// 列表数据
const dataList = ref()
const dataType = ref('newtask')
const dataCount = ref(0)
const show_loading = ref(false)
const key = ref(0)

interface itemType {
  titleList: []
  itemStyle: {
    modelStyle: ''
    titleStyle: ''
    listStyle: ''
    dateFormat: ''
  }
  isShow: {
    showCount: boolean
  }
}
const props = defineProps<{ itemJson: itemType }>()

emitter.on('workList', (obj: itemType) => {
  init(obj)
})
emitter.on('set4AcookieOK', () => {
  is4Alogin.value = true
})

onUnmounted(() => {
  emitter.off('workList')
  emitter.off('set4AcookieOK')
})

// 监听socket
watch(messageInfo, () => {
  handleSocket(messageInfo.value)
})

const handleSocket = (obj) => {
  if ('task' == obj.type) {
    loadTask(0, 'newtask') //更新任务
  }

  if ('completetask' == obj.type) {
    loadTask(0, 'completetask') //更新任务
  }

  if ('focus' == obj.type) {
    loadTask(0, 'focus') //更新任务
  }
}

// 标题数据处理
const titleList = ref([
  { id: '0', sign: 'newtask', title: '我的待办', count: 0, isShow: true },
  { id: '1', sign: 'completetask', title: '我的已办', count: 0, isShow: true },
  { id: '2', sign: 'focus', title: '我的关注', count: 0, isShow: true }
])
// 对象样式
const itemStyle = ref({
  modelStyle: {},
  titleStyle: {},
  listStyle: {},
  dateFormat: ''
})

const isShow = ref({
  showCount: true
})

//待办、已办、我的关注
const loadTask = async (index, type) => {
  nowWork.value = type
  refreshData()
  show_loading.value = true
  key.value = key.value + 1

  const title = titleList.value.filter((item) => item.sign == type)[0]
  //我的待办
  portal
    .wdgz_list({
      type: type,
      page: 1,
      pageSize: type === 'newtask' || type === 'completetask' ? 7 : 10,
      key: key.value
    })
    .then((res) => {
      let ret = res.data
      if (ret && res.key == key.value) {
        show_loading.value = false
        title.count = ret.total > 99 ? '99+' : ret.total //工作数量数量

        for (let i = 0; i < titleList.value.length; i++) {
          if (titleList.value[i].sign == type) {
            titleList.value[i] = title
          }
        }

        dataList.value = ret.records
        dataType.value = type
        dataCount.value = ret.total
      }
    })
    .catch(() => {
      show_loading.value = false
    })
}
// 刷新数据
const refreshData = () => {
  dataList.value = []
  dataType.value = 'newWork'
  dataCount.value = 0
}

//取消关注、关注
const FocusFlow = (type, item, tabName) => {
  let Title
  if (tabName == 'newtask') {
    Title = item.ActivityName
  } else if (tabName == 'completetask') {
    Title = item.TaskName
  } else if (tabName == 'apply') {
    Title = item.Title
  } else if (tabName == 'focus') {
    Title = item.FlowName
  }

  const msg = type == 0 ? '是否取消关注【' + Title + '】？' : '是否关注【' + Title + '】？'

  message.confirm(msg, '提示').then(() => {
    PortalApi.execMyFocus({
      type: type,
      tabName: tabName,
      myFlow: JSON.stringify(item)
    }).then((ret) => {
      if (ret) {
        message.success(type == 0 ? '已经取消关注！' : '关注成功，可在【我的关注】列表查看 ')
        if (tabName == 'newtask') {
          loadTask(0, tabName)
        } else if (tabName == 'completetask') {
          loadTask(1, tabName)
        } else if (tabName == 'focus') {
          loadTask(2, tabName)
        }
      } else {
        ElNotification({
          title: 'Success',
          message: 'This is a success message',
          type: 'error'
        })
      }
    })
  })
}
const init = (itemJson: itemType) => {
  if (itemJson) {
    titleList.value = itemJson.titleList
    itemStyle.value = itemJson.itemStyle
    isShow.value = itemJson.isShow
  }
}
onMounted(() => {
  init(props.itemJson)
  loadTask(0, 'newtask')
  if (Cookies.get(CACHE_KEY.login4A) != null) {
    if (!Cookies.get(CACHE_KEY.login4A)) {
      setTimeout(() => {
        is4Alogin.value = Cookies.get(CACHE_KEY.login4A)
      }, 2000)
    } else {
      is4Alogin.value = Cookies.get(CACHE_KEY.login4A)
    }
  } else {
    is4Alogin.value = true
  }
})
</script>

<style lang="scss" scoped>
@import url('@/assets/css/master.css');
@import url('@/assets/css/admin.css');

// 空状态
.empty-img {
  text-align: center;
  margin-top: 8%;
  img {
    width: 150px;
  }
  div {
    color: rgb(153, 153, 153);
    margin-top: 10px;
    text-align: center;
  }
}
.formate {
  min-width: 180px;
  white-space: nowrap;
  display: inline-block;
  text-align: right;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
