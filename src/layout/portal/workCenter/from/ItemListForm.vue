<template>
  <div>
    <el-form :model="form" :rules="rules">
      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">标题显示</el-text>
        </el-col>
        <el-col :span="18">
          <el-form-item>
            <el-select
              v-model="value"
              multiple
              filterable
              allow-create
              default-first-option
              :reserve-keyword="false"
              placeholder="选择工作列表"
              @change="titleChange"
            >
              <el-option
                v-for="item in option"
                :key="item.id"
                :label="item.title"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">标题样式</el-text>
        </el-col>
        <el-col :span="18">
          <el-input
            v-model="form.itemStyle.titleStyle"
            autosize
            type="textarea"
            placeholder="请输入css样式"
          />
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">列表样式</el-text>
        </el-col>
        <el-col :span="18">
          <el-input
            v-model="form.itemStyle.listStyle"
            autosize
            type="textarea"
            placeholder="请输入css样式"
          />
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">日期格式</el-text>
        </el-col>
        <el-col :span="18">
          <el-select v-model="form.itemStyle.dateFormate" placeholder="请选择" clearable>
            <el-option
              v-for="item in dateOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import emitter from '@/utils/mitt'

defineOptions({
  name: 'ItemListForm'
})
const props = defineProps<{ itemJson: any }>()

const option = ref([
  { id: '0', title: '院发文' },
  { id: '1', title: '院通知' },
  { id: '2', title: '院公告' }
])

const dateOption = [
  { label: '年-月-日 时:分:秒', value: 'YYYY-MM-DD HH:mm:ss' },
  { label: '年-月-日', value: 'YYYY-MM-DD' },
  { label: '年-月', value: 'YYYY-MM' },
  { label: '月-日', value: 'MM-DD' }
]
const value = ref()

const form = ref({
  titleList: [{ id: '0', title: '院发文' }],
  itemStyle: {
    titleStyle: '',
    listStyle: '',
    dateFormate: ''
  },
  isShow: {
    titleIsShow: true
  }
})

const rules = ref({})

defineExpose({ form })

// 状态监听，暴露给父类
watch(
  form,
  () => {
    emitter.emit('itemList', form.value)
  },
  { immediate: true, deep: true }
)

const titleChange = () => {
  let data = option.value.filter((item) => value.value.indexOf(item.id) !== -1)
  form.value.titleList = data
}
const setValue = () => {
  value.value = form.value.titleList.map((item) => item.id)
}
onMounted(() => {
  if (props.itemJson) {
    form.value = props.itemJson
  }
  setValue()
})

</script>

<style lang="scss" scoped>
.el-col {
  margin-bottom: 10px;
}
</style>
