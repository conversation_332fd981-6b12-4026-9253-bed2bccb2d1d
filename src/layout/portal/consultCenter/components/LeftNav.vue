<template>
  <div ref="boxRef" class="left-nav" :style="itemStyle.modelStyle">
    <div class="siderbar">
      <ul>
        <li class="p_item" v-if="isShow.showMywork">
          <a class="m_a" :href="toIndex()" title="回到办公中心">
            <img class="icon" src="@/assets/icons/portal/icon-mywork.png" />
            <span class="text" :style="itemStyle.titleStyle">我的工作</span>
          </a>
          <div class="ul_sub">
            <a
              :style="itemStyle.spanStyle"
              v-for="work in listWork"
              :key="work.type"
              :class="menu_actived == work.type ? 'actived' : ''"
              @click="db_open(work.type)"
              >{{ work.title }}</a
            >
          </div>
        </li>
        <li class="p_item" v-if="isShow.showRanKing">
          <a
            :class="is_open ? 'open m_a expend' : 'm_a expend'"
            href="#"
            @click="menu_click"
            style="margin-bottom: 10px"
          >
            <img class="icon" src="@/assets/icons/portal/icon-cyrk.png" />
            <span class="text" :style="itemStyle.titleStyle">点击排行</span>
          </a>
          <ul class="ul_sub ul_sub_sort" :style="{ height: is_open ? '360px' : '0' }">
            <li v-for="(dm, didx) in rankingList" :key="didx">
              <a
                :style="itemStyle.spanStyle"
                target="_blank"
                v-text="dm.FuncName"
                @click="handelhref(dm.Url)"
              ></a>
            </li>
          </ul>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getAccessToken } from '@/utils/auth'
import * as portal from '@/api/portal'
import { useAppStore } from '@/store/modules/app'
//点击菜单状态
const menu_actived = ref()
const is_open = ref(true)
const baseUrl = import.meta.env.VITE_TOURL_PREFIX
const { push } = useRouter() // 路由import emitter from '@/utils/mitt'
import emitter from '@/utils/mitt'
import { useResizeObserver } from '@vueuse/core'
import { debounce } from 'min-dash'
import { CATEGORY, useLayoutCache } from '@/hooks/web/categoryCache'
import { searchStore } from '@/store/modules/search'
const { wsCache } = useLayoutCache()
const _search = searchStore()
defineOptions({
  name: 'LeftNav'
})

const boxRef = ref(null)

const itemStyle = ref({
  modelStyle: {},
  titleStyle: {},
  spanStyle: {}
})
const isShow = ref({
  showRanKing: true,
  showMywork: true
})

const toIndex = () => {
  if (wsCache.get(CATEGORY.IsLayout)) {
    return '/Portal/newsLayout'
  } else {
    return '/Portal/WorkCenter/Index'
  }
}

//我的工作
const listWork = ref()

//点击排行
const rankingList = ref()

interface itemType {
  titleList: []
  itemStyle: {
    modelStyle: ''
    titleStyle: ''
    spanStyle: ''
  }
  isShow: {
    showRanKing: boolean
    showMywork: boolean
  }
}

const props = defineProps<{ itemJson: itemType; itemId: number }>()

emitter.on('updater', (obj: any) => {
  console.log(obj)
  init(obj)
})

onUnmounted(() => {
  emitter.off('updater')
})

const db_open = (type: string) => {
  menu_actived.value = type
  useAppStore().work_menu = type

  push('/workCenter/myWork')
}

const handelhref = (url: string) => {
  let toUrl
  if (url.includes('?')) {
    toUrl = baseUrl + url + '&token=' + getAccessToken()
  } else {
    toUrl = baseUrl + url + '?token=' + getAccessToken()
  }
  //替换//
  toUrl = toUrl.replace(/([^:]\/)\/+/g, '$1')
  window.open(toUrl, '_blank')
}

const menu_click = () => {
  is_open.value = !is_open.value
}

//导航数据
const loadCYRK = () => {
  portal.djph_list({}).then((ret) => {
    rankingList.value = ret
  })
}

const init = (itemJson: itemType) => {
  if (itemJson) {
    listWork.value = itemJson.titleList
    itemStyle.value = itemJson.itemStyle
    console.log(itemStyle.value)
    isShow.value = itemJson.isShow
  }
}
onMounted(() => {
  loadCYRK()
  init(props.itemJson)
})
useResizeObserver(boxRef, (entries) => {
  debouncedEmit(entries)
})
const debouncedEmit = debounce((entries) => {
  const entry = entries[0]
  const { height } = entry.contentRect
  emitter.emit('navHeightChange', { height: height, itemId: props.itemId })
}, 1)
</script>

<style lang="scss" scoped>
.left-nav {
  display: flex;
  justify-content: space-between;
}
ul {
  margin: 0;
  padding: 0;
  width: 100%;
  list-style: none;
  width: 100%;
}

.siderbar {
  display: flex;
  box-sizing: border-box;
  margin-top: 10%;
  width: 100%;
  ul {
    margin: 0 !important;
    padding: 0 !important;
  }

  .p_item {
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-top: 20px;
    .m_a {
      display: inline-block;
      color: black;
      font-weight: 600;
      text-decoration: none;
      display: flex;
      align-items: center;
      height: 30px;

      &.expend::after {
        background-image: url(data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2214%22%20height%3D%2214%22%20viewBox%3D%220%200%2014%2014%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%20%20%20%20%3Cpolyline%20fill%3D%22none%22%20stroke%3D%22%23666%22%20stroke-width%3D%221.1%22%20points%3D%2210%201%204%207%2010%2013%22%20%2F%3E%0A%3C%2Fsvg%3E);
        background-repeat: no-repeat;
        background-position: 50% 50%;
        transform: rotate(180deg);
        transition: transform 0.4s ease-out;
        width: 1.5em;
        height: 1.5em;
        content: '';
        margin-left: 25px;
      }

      &.open::after {
        transform: rotate(90deg);
      }

      .icon {
        margin-left: 40px;
      }

      .text {
        font-size: 15px;
        margin-left: 6px;
        color: #000;
      }
    }

    .ul_sub {
      transition: height 0.3s ease-out;
      overflow: hidden;
      margin-left: -30px;
      display: flex;
      flex-direction: column;
      margin: 0 !important;
      padding: 0 !important;

      a {
        height: 36px;
        line-height: 36px;
        width: 100%;
        width: 100%;
        height: 100%;
        display: inline-block;
        color: #999;
        font-weight: normal;
        padding-left: 64px;
        text-decoration: none;
        cursor: pointer;
        border-left: 2px solid #fff;

        &:hover {
          color: #333 !important;
        }

        &.actived {
          border-left: 2px solid #0070ff;
          background-color: #f4faff;
          color: #0070ff;
          font-weight: 600;
        }
      }
    }
  }
}
</style>
