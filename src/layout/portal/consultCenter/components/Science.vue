<template>
  <!--科技创新园地-->
  <div class="hbm-zxzx-sec hbm-zxzx-sec5">
    <div class="hbm-zxzx-row">
      <div class="hbm-zxzx-cell" style="width: 100% !important">
        <div class="gcs_tab_kjcxyd gcs_tab_zxzx_single" cty="tab" grp="zxzx_kjcxyd">
          <div class="gcs_tabhd" grp="zxzx_kjcxyd">
            <ul>
              <li class="gcs_tabhd_item gcs_cur" atr="yfw">
                科技创新园地
                <div class="gcs_cur_inner"></div>
              </li>
            </ul>
            <a
              v-cloak
              v-if="kjcxy && kjcxy.length > 0"
              :href="'/Portal/NewsCenter/PublicInfoList?code=' + kjcxy[tab3_index]['Code']"
              target="__blank"
              class="hbm-moreico fa fa-list-ul"
            ></a>
          </div>

          <div class="gcs_tabbd" grp="zxzx_kjcxyd">
            <div class="gcs_tabitem" grp="zxzx_kjcxyd" atr="yfw">
              <div class="gcs_tab_kjcxyd_childs" cty="tab">
                <div class="gcs_tabhd">
                  <ul v-cloak v-if="kjcxy && kjcxy.length > 0">
                    <li
                      @click="tab3_index = index"
                      :class="index == tab3_index ? 'gcs_tabhd_item gcs_cur' : 'gcs_tabhd_item'"
                      atr="item1"
                      v-for="(item, index) in kjcxy"
                      :key="index"
                    >
                      <span class="icon"></span>
                      <span class="gcs_tabliico"></span><span v-text="item.CatalogName"></span>
                    </li>
                  </ul>
                  <el-empty v-else description="数据为空" />
                </div>

                <div class="gcs_tabbd">
                  <div class="gcs_tabitem" v-if="kjcxy && kjcxy.length > 0" v-cloak>
                    <div
                      class="gcs_newslist gcs_newslist_kjcx_dynamic"
                      v-for="(item, index) in kjcxy"
                      :key="index"
                    >
                      <templatete v-if="index == tab3_index">
                        <div
                          v-for="(i, idx) in item.list"
                          :key="idx"
                          class="gcs_newslist_item"
                          cid="77783"
                          :title="i.Title"
                        >
                          <span class="gcs_title bgtitle">
                            <a
                              :style="itemStyle.spanStyle"
                              :href="detail_url(item.Code, i.ID, item.CatalogName)"
                              target="_blank"
                              :title="i.Title"
                              v-text="i.Title"
                            ></a>
                          </span>
                          <span
                            :style="itemStyle.spanStyle"
                            class="gcs_dt"
                            v-text="formatDate(i.CreateTime, itemStyle.dateFormate)"
                          ></span>
                        </div>
                      </templatete>
                    </div>
                  </div>
                  <el-empty v-else description="数据为空" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { formatDate } from '@/utils/formatTime'
import { detail_url } from '@/layout/portal/admin'
import * as PortalApi from '@/api/system/portal'
import emitter from '@/utils/mitt'

defineOptions({
  name: 'Science'
})

interface itemType {
  itemStyle: {
    modelStyle: ''
    titleStyle: ''
    spanStyle: ''
    dateFormate: ''
  }
  isShow: {}
}

const itemStyle = ref({
  modelStyle: {},
  titleStyle: {},
  spanStyle: {},
  dateFormate: 'MM-DD'
})

const isShow = ref({})

const props = defineProps<{ itemJson: itemType }>()

const tab3_index = ref(0) //科技创新园tab切换索引

const kjcxy = ref()

emitter.on('science', (obj: itemType) => {
  init(obj)
})

//解除绑定事件
onUnmounted(() => {
  emitter.off('science')
})

//科技创新园地
const get_kjcxy = async () => {
  let result = await PortalApi.newsCenterGetTechnologicalInnovation({ pageSize: 11 })
  kjcxy.value = result
}

const init = (itemJson: itemType) => {
  if (itemJson) {
    if (itemJson.itemStyle) {
      itemStyle.value = itemJson.itemStyle
    }
    if (itemJson.isShow) {
      isShow.value = itemJson.isShow
    }
  }
}

onMounted(() => {
  init(props.itemJson)
  get_kjcxy()
})
</script>

<style lang="scss" scoped>
@import url('@/assets/css/admin.css');
@import url('@/assets/css/bootstrap.min.css');
@import url('@/assets/css/font-awesome.css');

.gcs_dt {
  display: inline-block;
  text-align: right;
  min-width: 180px;
  //margin-left: 20px;
  white-space: nowrap; /* 禁止换行 */
  overflow: hidden; /* 隐藏溢出内容 */
  text-overflow: ellipsis; /* 使用省略号表示溢出内容 */
}
.gcs_tab_kjcxyd_childs .gcs_tabhd {
  float: left;
  min-width: 260px;
}
.gcs_tabbd {
  background-color: #fff;
  height: 93vh;
}
.gcs_title:hover {
  color: #000;
}

.gcs_title a {
  font-size: 16px;
  width: 300px;
  text-decoration: none;
  cursor: pointer;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  color: #555;
  margin-left: 3%;
}
.gcs_tabhd .icon {
  width: 6.5px;
  height: 6.5px;
  background-color: #d8ddec;
  margin-right: 10px;
  border-radius: 50%;
  margin-left: 15px;
}
</style>
