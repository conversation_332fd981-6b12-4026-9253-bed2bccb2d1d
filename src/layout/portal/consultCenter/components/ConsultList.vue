<template>
  <div class="uk-flex work-top" id="workTop" :style="itemStyle.modelStyle">
    <div class="uk-card uk-card-default work-card">
      <!-- 多标题样式 -->
      <div class="uk-flex uk-card-header" v-if="titleList.length > 1">
        <div
          :class="nowTitle.id == item.id ? 'active_2' : ''"
          v-for="(item, index) in titleList"
          :key="index"
          @click="loadYNXX(index, item.id)"
          ><span :class="nowTitle.id == item.id ? 'active_2' : ''">{{ item.title }}</span>
        </div>
        <a target="_blank" class="hbm-moreico fa fa-list-ul" @click="more()"></a>
      </div>

      <!-- 单标题样式 -->
      <div class="uk-flex uk-card-header" v-else>
        <div>
          <span class="uk-card-title">{{ titleList[0].title }}</span>
          <div class="gcs_cur_inner"></div>
        </div>
        <a target="_blank" class="hbm-moreico fa fa-list-ul" @click="more()"></a>
      </div>
      <div class="gcs_zxzx_ss_body">
        <ul class="article_list" v-if="YNXXList && YNXXList.length > 0" v-cloak>
          <li class="item has_icon" v-for="(item, index) in YNXXList" :key="index">
            <i class="top" v-if="item.IsTop > 0" v-cloak> </i>
            <a
              class="link"
              @click="openDetail(nowTitle.id, item.ID, nowTitle.name)"
              target="_blank"
              :title="item.Title"
              v-text="item.Title"
            ></a>
            <span
              class="datetime"
              v-text="formatDate(item.CreateTime, itemStyle.dateFormate)"
            ></span>
          </li>
        </ul>
        <el-empty v-else description="数据为空" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as PortalApi from '@/api/system/portal'
import { detail_url } from '@/layout/portal/admin'
import { formatDate } from '@/utils/formatTime'
import emitter from '@/utils/mitt'
const YNXXList = ref()

defineOptions({
  name: 'ConsultList'
})

interface itemType {
  titleList: []
  itemStyle: {
    modelStyle: ''
    titleStyle: ''
    spanStyle: ''
    dateFormate: ''
  }
  isShow: {}
}

const props = defineProps<{ itemJson: itemType }>()

const itemStyle = ref({
  modelStyle: '',
  titleStyle: '',
  spanStyle: '',
  dateFormate: 'MM-DD'
})

interface titType {
  id: string
  title: string
  name: string
}

// 标题列表
const titleList = ref<Array<titType>>([{ id: '', title: '', name: '' }])

// 当前标题id
const nowTitle = ref()

const loadYNXX = async (index, id: string) => {
  nowTitle.value = titleList.value.filter((item) => item.id == id)[0]

  YNXXList.value = []

  // 查询列表数据
  const res = PortalApi.newsCenterGetNewsList({ code: id, pageSize: 6 })
  res.then((item) => {
    YNXXList.value = item.records
  })
}

// 更多信息
const more = async () => {
  const url = '/Portal/NewsCenter/PublicInfoList?code=' + nowTitle.value.id
  window.open(url, '_blank')
}

// 打开详情
const openDetail = async (code: string, id: string, cate: string) => {
  const url = detail_url(code, id, cate)
  window.open(url, '_blank')
}

const init = (itemJson: itemType) => {
  if (itemJson) {
    titleList.value = itemJson.titleList
    if (itemJson.itemStyle) {
      itemStyle.value = itemJson.itemStyle
    }
    console.log("==>"+itemJson)
    console.log(itemStyle.value)
  }
}

onMounted(() => {
  init(props.itemJson)
  loadYNXX(0, titleList.value[0].id)
})

emitter.on('consult', (obj: itemType) => {
  console.log(obj)
  init(obj)
})

onUnmounted(() => {
  emitter.off('consult')
})
</script>

<style lang="scss" scoped>
@import url('@/assets/css/master.css');
@import url('@/assets/css/bootstrap.min.css');
@import url('@/assets/css/admin.css');
@import url('@/assets/css/font-awesome.css');

.work-top {
  width: 100%;
  padding: 0;
}

.fa {
  margin-top: 15px;
}

.fa-list-ul:before {
  content: '\f0ca';
}

.hbm-moreico {
  text-decoration: none !important;
}

.hbm-moreico {
  top: 20px;
  right: 20px;
  position: absolute;
  font-size: 13px;
  color: #afafaf;
  cursor: pointer;
}

.hbm-moreico:hover {
  color: #363636;
}

.gcs_cur_inner {
  position: absolute;
  width: 68px;
  height: 4px;
  background: #0d5ffe;
  box-shadow: 0px 2px 3px 0px rgba(0, 112, 255, 0.2);
  border-radius: 0px 0px 4px 3px;
  top: 36px;
  left: 20px;
}
</style>
