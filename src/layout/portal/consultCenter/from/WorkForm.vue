<template>
  <div>
    <el-form :model="form" :rules="rules">
      <el-row>
        <el-col :span="18">
          <el-form-item label="日期格式" prop="dateFormate">
            <el-select
              v-model="form.dateFormate"
              placeholder="请选择"
              clearable
              @change="dateChange"
            >
              <el-option
                v-for="item in dateOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { dateOption } from '@/utils/formatTime';
import emitter from '@/utils/mitt'

defineOptions({
  name: 'WorkForm'
})

const form = ref({
  dateFormate: 'YYYY-MM-DD HH:mm:ss'
})
const rules = ref({})

defineExpose({ form })

const dateChange = (value: string) => {
  emitter.emit('mywork', { dateFormate: value })
}
</script>
