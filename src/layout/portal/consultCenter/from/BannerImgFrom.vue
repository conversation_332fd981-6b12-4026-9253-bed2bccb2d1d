<template>
  <div>
    <el-form :model="form">
      <el-row>
        <el-col :span="22">
          <el-text class="mx-1">执行脚本参数</el-text>
        </el-col>
        <el-col>
          <el-input
            v-model="jsonString"
            style="width: 240px"
            autosize
            placeholder="默认"
            type="textarea"
            clearable
            @change="imageListChange"
          />
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">走马灯高度</el-text>
        </el-col>
        <el-col>
          <el-input
            v-model="form.itemStyle.carouselHeadel"
            style="width: 240px"
            placeholder="输入高度px"
            clearable
          />
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">图片样式</el-text>
        </el-col>
        <el-col>
          <el-input
            v-model="form.itemStyle.imgStyle"
            style="width: 240px"
            autosize
            type="textarea"
            placeholder="请输入css样式"
          />
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="18">
          <el-text class="mx-1">切换间隔</el-text>
        </el-col>
        <el-col>
          <el-input
            v-model="form.itemStyle.interval"
            style="width: 240px"
            autosize
            placeholder="请输入时间间隔毫秒"
          />
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="18">
          <el-checkbox
            label="是否显示下边栏"
            v-model="form.isShow.titleIsShow"
            :value="!form.isShow.titleIsShow"
          />
        </el-col>
      </el-row>

      <el-row v-if="form.isShow.titleIsShow">
        <el-col :span="18">
          <el-text class="mx-1">下边栏样式</el-text>
        </el-col>
        <el-col>
          <el-input
            v-model="form.itemStyle.btmStyle"
            style="width: 240px"
            autosize
            type="textarea"
            placeholder="请输入css样式"
          />
        </el-col>
        <el-col :span="18">
          <el-text class="mx-1">下边栏标题样式</el-text>
        </el-col>
        <el-col>
          <el-input
            v-model="form.itemStyle.titleStyle"
            style="width: 240px"
            autosize
            type="textarea"
            placeholder="请输入css样式"
          />
        </el-col>
        <el-col :span="18">
          <el-text class="mx-1">下边栏链接样式</el-text>
        </el-col>
        <el-col>
          <el-input
            v-model="form.itemStyle.moreStyle"
            style="width: 240px"
            autosize
            type="textarea"
            placeholder="请输入css样式"
          />
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import emitter from '@/utils/mitt'

defineOptions({
  name: 'ImageCardForm'
})
const props = defineProps<{ itemJson: any }>()

const form = ref({
  titleList: { code: 'SQL_ae0c00eda596438f90547de550efa3ed', ExecData: '' },
  itemStyle: {
    titleStyle: '',
    carouselHeadel: '',
    btmStyle: '',
    imgStyle: '',
    moreStyle: '',
    interval: 1000
  },
  isShow: {
    titleIsShow: true
  }
})
const jsonString = ref()
// 状态监听，暴露给父类
watch(
  form,
  () => {
    emitter.emit('bannerImg', form.value)
  },
  { deep: true }
)

const imageListChange = () => {
  if (jsonString.value) {
    form.value.titleList = JSON.parse(jsonString.value)
  }
}

defineExpose({ form })

onMounted(() => {
  if (props.itemJson) {
    form.value = props.itemJson
  }
  setImageData()
})

const setImageData = () => {
  let jsonStr = JSON.stringify(form.value.titleList)
  jsonString.value = jsonStr
}
</script>

<style lang="scss" scoped>
.el-col {
  margin-bottom: 10px;
}
</style>
