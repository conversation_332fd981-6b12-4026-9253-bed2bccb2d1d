<template>
  <div class="my_work">
    <div class="header flex f_j_b">
      <div class="left flex">
        <div class="text">{{ nowTitle.title }}</div>
        <div class="count">({{ total }})</div>
      </div>

      <label class="right flex">
        <input class="checkbox" type="checkbox" @change="onTitleToggle" />
        <span>显示完整标题</span>
      </label>
    </div>

    <div class="task">
      <div class="list flex f_c">
        <!--我的待办-->
        <template v-if="menu_actived === 'newtask'">
          <div
            class="item flex f_j_b"
            v-for="(item, index) in nowList"
            :key="index"
            style="cursor: pointer"
            @click="OpenView(2, item)"
          >
            <div class="icon">
              <img
                title="取消关注"
                v-if="item.IsFocus == 'true'"
                @click.stop="FocusFlow(0, item, menu_actived)"
                src="@/assets/icons/portal/icon-tuding-copy.png"
              />
              <img
                title="关注任务"
                v-else-if="item.IsFocus == 'false'"
                @click.stop="FocusFlow(1, item, menu_actived)"
                src="@/assets/icons/portal/icon-tuding.png"
              />
            </div>

            <div class="title" v-text="item.ActivityName"></div>
            <div class="cate">
              <span v-if="item.GoodWaySoft === 'RIIT'">信息院综合管理系统</span>
              <span v-else-if="item.GoodWaySoft === 'PC_SRMSFlow'">科技系统</span>
              <span v-else-if="item.GoodWaySoft === 'JiuQiCaiWu'">财务系统</span>
              <span v-else-if="item.GoodWaySoft === 'PC_PRPFlow'">EPC系统</span>
              <span v-else :title="item.FlowName">{{ item.FlowName }}</span>
            </div>
            <div class="info">
              <span class="dept" :title="item.FromDeptName" v-if="item.FromDeptName"
                >{{ item.ShortDeptName }}：</span
              >
              <span class="dept" v-else></span>
              <span class="name" v-if="item.FromUserNames">{{ item.FromUserNames }}</span>
              <span class="name" v-else></span>
              <span class="time" v-text="formatDate(item.CreateTime, nowDateFormat)"></span>
            </div>
          </div>
        </template>

        <!--我的已办-->
        <template v-if="menu_actived === 'completetask'">
          <div
            class="item flex f_j_b"
            v-for="(item, index) in nowList"
            :key="index"
            style="cursor: pointer"
            @click="OpenView(2, item)"
          >
            <div class="icon">
              <img
                title="取消关注"
                v-if="item.IsFocus == 'true'"
                @click.stop="FocusFlow(0, item, menu_actived)"
                src="@/assets/icons/portal/icon-tuding-copy.png"
              />
              <img
                title="关注任务"
                v-else-if="item.IsFocus == 'false'"
                @click.stop="FocusFlow(1, item, menu_actived)"
                src="@/assets/icons/portal/icon-tuding.png"
              />
            </div>

            <div class="title" v-text="item.TaskName"></div>
            <div class="cate">
              <span v-if="item.GoodWaySoft === 'PC_SRMSFlow'">信息院综合管理系统</span>
              <span v-else-if="item.GoodWaySoft === 'KJXT'">科技系统</span>
              <span v-else-if="item.GoodWaySoft === 'JiuQiCaiWu'">财务系统</span>
              <span v-else-if="item.GoodWaySoft === 'PC_PRPFlow'">EPC系统</span>
              <span v-else :title="item.FlowName">{{ item.FlowName }}</span>
            </div>
            <div class="info">
              <span class="dept" :title="item.CreateDeptName" v-if="item.CreateDeptName"
                >{{ item.CreateUserDeptName }}：</span
              >
              <span class="name">{{ item.CreateUserName }}</span>
              <span class="time">{{ item.ExecTime.substring(0, 16) }}</span>
            </div>
          </div>
        </template>

        <!--我的申请-->
        <template v-if="menu_actived === 'apply'">
          <div
            class="item flex f_j_b"
            v-for="(item, index) in nowList"
            :key="index"
            style="cursor: pointer"
            @click="OpenView(2, item)"
          >
            <div class="icon">
              <img
                title="取消关注"
                v-if="item.IsFocus == true"
                @click.stop="FocusFlow(0, item, menu_actived)"
                src="@/assets/icons/portal/icon-tuding-copy.png"
              />
              <img
                title="关注任务"
                v-else-if="item.IsFocus == false"
                @click.stop="FocusFlow(1, item, menu_actived)"
                src="@/assets/icons/portal/icon-tuding.png"
              />
            </div>
            <div
              class="title"
              :title="item.Title"
              style="overflow: hidden; white-space: nowrap; text-overflow: ellipsis"
              >{{ item.Title }}</div
            >
            <div
              class="info apply-flow"
              style="display: flex; flex-direction: column; align-items: flex-start"
            >
              <div>
                <span class="k">审批状态：</span>
                <span :title="item.FlowState">{{ item.FlowState }}</span>
              </div>
              <div>
                <span class="k">关注日期：</span>
                <span>{{ item.CreateTime.split(' ')[0] }}</span>
              </div>
              <div>
                <span class="k">当前审批步骤：</span>
                <span :title="item.CurrentStep">{{ item.CurrentStep }}</span>
              </div>
            </div>
          </div>
        </template>

        <!--我的文件-->
        <template v-if="menu_actived === 'myfile'">
          <div
            class="item flex f_j_b"
            v-for="(item, index) in nowList"
            :key="index"
            style="cursor: pointer"
            @click="OpenView(2, item)"
          >
            <div class="icon">
              <img :src="getFullUrl(item.Icon)" />
            </div>
            <div class="title" :title="item.FileName.substring(item.FileName.indexOf('_') + 1)">
              {{ item.FileName.substring(item.FileName.indexOf('_') + 1) }}</div
            >
            <div class="info">
              <img
                @click.stop="DownLoadAndPreviewFile(item, 0)"
                src="@/assets/imgs/icon-download.png"
                title="下载文件"
              />
              <img
                @click.stop="CancelSc(item)"
                style="margin-left: 20px; width: 16px; height: 16px"
                title="取消收藏"
                src="@/assets/imgs/sc_a.png"
              />
              <img
                title="取消关注"
                v-if="item.IsFocus == 'true'"
                @click.stop="FocusFlow(0, item, menu_actived)"
                src="@/assets/icons/portal/icon-tuding-copy.png"
              />
              <img
                title="关注任务"
                v-else-if="item.IsFocus == 'false'"
                @click.stop="FocusFlow(1, item, menu_actived)"
                src="@/assets/icons/portal/icon-tudingwb.png"
              />
            </div>
          </div>
        </template>

        <!--我的关注-->
        <template v-else-if="menu_actived === 'focus'">
          <div
            class="item flex f_j_b"
            v-for="(item, index) in nowList"
            :key="index"
            style="cursor: pointer"
            @click="OpenView(2, item)"
          >
            <div class="icon">
              <img
                @click.stop="FocusFlow(0, item, menu_actived)"
                src="@/assets/icons/portal/icon-tuding-copy.png"
              />
            </div>

            <div
              class="title"
              :title="item.FlowName"
              style="overflow: hidden; white-space: nowrap; text-overflow: ellipsis"
              >{{ item.FlowName }}</div
            >

            <div
              class="info apply-flow"
              style="display: flex; flex-direction: column; align-items: flex-start"
            >
              <div>
                <span class="k">审批状态：</span>
                <span :title="item.FlowState">{{ item.FlowState }}</span>
              </div>

              <div>
                <span class="k">关注日期：</span>
                <span>{{ item.CreateTime.split(' ')[0] }}</span>
              </div>

              <div>
                <span class="k">当前审批步骤：</span>
                <span :title="item.CurrentStep">{{ item.CurrentStep }}</span>
              </div>
            </div>
          </div>
        </template>
      </div>

      <div
        class="emptybox"
        v-if="loading"
        style="margin-top: 200px; text-align: center; color: red"
      >
        <div class="line-scale loading_icon">
          <!-- 背景样式失效，添加加载颜色背景 -->
          <div style="background-color: #888; margin-right: 8px"></div>
          <div style="background-color: #888; margin-right: 8px"></div>
          <div style="background-color: #888; margin-right: 8px"></div>
          <div style="background-color: #888; margin-right: 8px"></div>
          <div style="background-color: #888; margin-right: 8px"></div>
        </div>
      </div>

      <template v-if="total <= 0 && !loading">
        <div class="no-data" style="padding-top: 200px; text-align: center">
          <img src="@/assets/imgs/nodata.png" width="100px" height="80px" />
          <div class="text" style="color: #999; margin-top: 10px">暂无数据</div>
        </div>
      </template>

      <div class="pager" >
        <el-pagination
          v-model:current-page="form_data.page"
          v-model:page-size="form_data.pageSize"
          :total="total"
          style="text-align: center; margin-top: 20px"
          layout="jumper, prev, pager, next, total"
          @current-change="currentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as PortalApi from '@/api/system/portal'
import * as portal from '@/api/portal'
import { CancelSc, DownLoadAndPreviewFile, getFullUrl, OpenView } from '@/layout/portal/admin'
import { useAppStore } from '@/store/modules/app'
import { useMessage } from '@/hooks/web/useMessage'
import { formatDate } from '@/utils/formatTime'
import emitter from '@/utils/mitt'
import {messageInfo} from '@/utils/websocket'
const message = useMessage()

//获取store里菜单被选中项目
const menu_actived = computed(() => {
  if (useAppStore().work_menu == '' || useAppStore().work_menu == undefined) {
    //没有状态的时候，默认待办
    useAppStore().work_menu = 'newtask'
    return 'newtask'
  }
  return useAppStore().work_menu
})

defineOptions({
  name: 'MyWork'
})

// 当前标题
const nowTitle = ref({
  id: '',
  sign: '',
  title: '',
  count: 0,
  isShow: false
})
//列表查询参数
let form_data = ref({
  type: menu_actived.value,
  page: 1,
  pageSize: 10,
  key: 0
})

// 列表数据
const nowList = ref()
const loading = ref(false)
const total = ref(0)

// 标题数据处理
const titleList = ref([
  { id: '0', sign: 'newtask', title: '我的待办', count: 0, isShow: true },
  { id: '1', sign: 'completetask', title: '我的已办', count: 0, isShow: true },
  { id: '2', sign: 'myfile', title: '我的文件', count: 0, isShow: false },
  { id: '3', sign: 'apply', title: '我的申请', count: 0, isShow: true },
  { id: '4', sign: 'focus', title: '我的关注', count: 0, isShow: true }
])

// 处理当前工作
const handleMenuWork = () => {
  nowTitle.value = titleList.value.filter((item) => item.sign == menu_actived.value)[0]

  // 查询工作信息
  console.log(nowTitle.value)
  get_list()
}

// 分页监听
const currentChange = () => {
  get_list()
}

//api获取列表数据
const get_list = async () => {
  form_data.value.type = menu_actived.value
  nowList.value = []
  loading.value = true
  form_data.value.key = form_data.value.key + 1
  try {
    const ret = await portal.wdgz_list(form_data.value)
    let res = ret.data
    if (res && ret.key == form_data.value.key) {
      nowList.value = res.records
      total.value = res.total != undefined ? res.total : 0
      form_data.value.page = res.current
    }
  } finally {
    loading.value = false
  }
}

//关注&取消关注
const FocusFlow = (type, item, tabName) => {
  var Title
  if (tabName == 'newtask') {
    Title = item.ActivityName
  } else if (tabName == 'completetask') {
    Title = item.TaskName
  } else if (tabName == 'apply') {
    Title = item.Title
  } else if (tabName == 'focus') {
    Title = item.FlowName
  }
  const msg = type == 0 ? '是否取消关注【' + Title + '】？' : '是否关注【' + Title + '】？'
  message.confirm(msg, '提示').then(() => {
    PortalApi.execMyFocus({
      type: type,
      tabName: tabName,
      myFlow: JSON.stringify(item)
    }).then((ret) => {
      if (ret) {
        message.success(type == 0 ? '已经取消关注！' : '关注成功，可在【我的关注】列表查看。')
        get_list()
      } else {
        ElNotification({
          title: 'Success',
          message: 'This is a success message',
          type: 'error'
        })
      }
    })
  })
}

// 显示完整标题
const onTitleToggle = (event) => {
  const isChecked = event.target.checked
  let dyc = document.getElementsByClassName('title')
  if (isChecked) {
    for (let item of dyc) {
      item.style.whiteSpace = 'normal'
    }
  } else {
    for (let item of dyc) {
      item.style.whiteSpace = 'nowrap'
    }
  }
}

//监听菜单项改变事项，默认初始化加载一次监听
watch(
  menu_actived,
  () => {
    handleMenuWork() //查询数据
  },
  { immediate: true }
)

// 监听socket
watch(messageInfo, () => {
  handleSocket(messageInfo.value)
})

const handleSocket = (obj) => {
  if ('task' == obj.type) {
    handleMenuWork()
  }
}

onMounted(() => {
  handleMenuWork()
})

// 日期格式化类型
const dateFormatType = ref(['YYYY-MM-DD HH:mm:ss', 'YYYY-MM', 'MM-DD'])
const nowDateFormat = ref(dateFormatType.value[0])

//配置属性================

emitter.on('mywork', (obj: any) => {
  if (obj.dateFormate) {
    let index = dateFormatType.value.indexOf(obj.dateFormate)
    nowDateFormat.value = dateFormatType.value[index]
  }
})
//解除绑定事件
onUnmounted(() => {
  emitter.off('mywork')
})
//=====================
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

.my_work {
  margin: 0 !important;
  padding: 0 !important;
  margin-left: auto !important;
  margin-top: 10px !important;
  margin-bottom: 20px !important;
  width: 100%;
  height: 100%;
  background: #fff;
  color: #666;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  position: relative;

  .flex {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .f_j_b {
    justify-content: space-between;
  }

  .f_c {
    flex-direction: column;
  }

  .header {
    height: 40px;
    border-bottom: 1px solid #e5e5e5;
    padding: 0 25px;

    .left {
      .text {
        font-size: 16px !important;
        font-weight: bold;
        color: #333;
      }
    }

    .right {
      font-weight: bold;
      font-size: 14px;
      color: #555;

      .checkbox {
        margin-right: 8px;
      }
    }
  }

  .list {
    .item {
      width: 100%;
      padding: 0 25px;
      margin: 0;
      padding: 20px;
      border-left: 2px solid #fff;
    }

    .title {
      margin-right: auto;
      color: rgb(85, 119, 255);
      width: 42%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      margin-right: auto;
    }

    .cate {
      width: 16% !important;
      text-align: center !important;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .info {
      margin-left: auto;
      width: 40% !important;
      text-align: right !important;
      display: flex;
      align-items: center;
      justify-content: flex-end;

      .dept {
        display: inline-block;
        width: 144px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .name {
        margin-left: 10px;
        width: 70px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        text-align: left;
      }

      .time {
        margin-left: 10px;
        width: 164px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    .icon {
      width: 16px;
      height: 15px;
      margin-right: 10px;

      img {
        width: auto;
        height: 18px;
      }
    }
  }

  .pager {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    bottom: 10%;
  }
}
</style>
