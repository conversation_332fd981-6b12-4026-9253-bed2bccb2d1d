<template>
  <el-tag
    v-if="shouldShowTag"
    :style="tagStyle"
    :type="tagType"
    :color="tagColor"
    disable-transitions
  >
    <slot v-if="$slots.default" :dictData="dictData" />
    <template v-else>{{ dictData?.label }}</template>
  </el-tag>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { isHexColor } from '@/utils/color'
import { getDictOptions } from '@/utils/dict'
import type { DictDataType } from '@/utils/dict'

const props = defineProps({
  type: {
    type: String,
    required: true
  },
  value: {
    type: [String, Number, Boolean],
    required: true
  }
})

const dictData = ref<DictDataType | null>(null)

const shouldShowTag = computed(() => {
  return props.type && props.value !== undefined && props.value !== null
})

const tagStyle = computed(() => {
  return dictData.value?.cssClass ? { color: '#fff' } : {}
})

const tagType = computed(() => {
  if (!dictData.value?.colorType) return ''
  const colorType = String(dictData.value.colorType)
  return colorType === 'primary' || colorType === 'default' ? '' : colorType
})

const tagColor = computed(() => {
  return dictData.value?.cssClass && isHexColor(dictData.value.cssClass) 
    ? dictData.value.cssClass 
    : ''
})

const fetchDictData = () => {
  if (!shouldShowTag.value) {
    dictData.value = null
    return
  }

  const dictOptions = getDictOptions(props.type)
  const foundDict = dictOptions.find(dict => dict.value === props.value.toString())
  
  if (foundDict) {
    dictData.value = { ...foundDict }
  } else {
    dictData.value = null
  }
}

// 监听 props 变化
watch(
  () => [props.type, props.value],
  () => fetchDictData(),
  { immediate: true }
)
</script>