//注册富文本 自定义工具
import {<PERSON><PERSON>,<PERSON><PERSON><PERSON>onM<PERSON>u,IDomEditor} from '@wangeditor/editor'
import {updateHelpDoc} from '@/api/portal'

class MyButtonMenu implements IButtonMenu {
  title: string
  tag: string;
  iconSvg: string;
  constructor() {
      this.title = '保存'
      this.iconSvg = '<svg t="1732761073092" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4666" id="mx_n_1732761073094" width="16" height="16"><path d="M819 65H205c-77.2 0-140 62.8-140 140v614c0 77.2 62.8 140 140 140h614c77.2 0 140-62.8 140-140V205c0-77.2-62.8-140-140-140z m-459.4 80h304.8v260.8c0 31.6-25.7 57.4-57.4 57.4H416.9c-31.6 0-57.4-25.7-57.4-57.4V145zM879 819c0 33.1-26.9 60-60 60H205c-33.1 0-60-26.9-60-60V205c0-33.1 26.9-60 60-60h74.6v260.8c0 75.7 61.6 137.4 137.4 137.4h190.1c75.7 0 137.4-61.6 137.4-137.4V145H819c33.1 0 60 26.9 60 60v614z" fill="#2b579a" p-id="4667"></path><path d="M574.3 393.1c22.1 0 40-17.9 40-40v-93.8c0-22.1-17.9-40-40-40s-40 17.9-40 40v93.8c0 22 17.9 40 40 40z" fill="#2b579a" p-id="4668"></path></svg>'
      this.tag = 'button'
  }
  getValue(editor: IDomEditor): string | boolean {                      
      return ''
  }
  isActive(editor: IDomEditor): boolean {                 
      return false
  }
  isDisabled(editor: IDomEditor): boolean {         
      return false
  }
  exec(editor: IDomEditor, value: string | boolean) {  
      editor.emit('updateDoc',editor)
      
  }

}
const menu = {
    key: 'my-menu-1',
    factory() {
      return new MyButtonMenu()
    },
  }
Boot.registerMenu(menu)