<template>
  <ul class="right-side">
    <!-- 我的待办 -->
    <li>
      <el-tooltip content="我的待办" :show-arrow="false">
        <el-badge :value="wddbCount" class="item" :max="99" type="info" @click="openTodo">
          <div class="message-box-trigger">
            <img src="@/assets/icons/portal/todo.png" class="icon"/>
          </div>
        </el-badge>
      </el-tooltip>
    </li>
    <!-- 系统消息 -->
    <li>
      <el-tooltip content="我的消息" :show-arrow="false">
        <el-badge
          :value="unreadMsgCount.NewMsgCount"
          class="item"
          :max="99"
          type="info"
          @click="openMsgPage">
          <div class="message-box-trigger">
            <img src="@/assets/icons/portal/info1.png" class="icon"/>
          </div>
        </el-badge>
      </el-tooltip>
    </li>
    <!-- 头像-->
    <li style="padding-right: 0"></li>
    <!-- 用户信息 -->
    <li style="padding-left: 3px">
      <el-dropdown @command="handleCommand">
        <div class="el-dropdown-link">
          <el-avatar :size="40" :src="headSrc" @error="errorHandler">
            <img src="@/assets/icons/portal/avatar.png"/>
          </el-avatar>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item :icon="UserFilled" command="a">
              <span>个人中心</span>
            </el-dropdown-item>
            <el-dropdown-item :icon="SwitchButton" command="b">
              <span>退出登录</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </li>
  </ul>
</template>
<script setup lang="ts">
import {SwitchButton, UserFilled, ArrowDown} from '@element-plus/icons-vue'
import {ElMessageBox} from 'element-plus'
import {useUserStore} from '@/store/modules/user'
import * as PortalApi from '@/api/system/portal'
import {getAccessToken} from '@/utils/auth'
import * as LogApi from '@/api/system/pageAndFuncLog'
import {useTaskStore} from '@/store/modules/task'
import {useAppStore} from "@/store/modules/app";
import {watch} from "vue";

const userStore = useUserStore()
const taskStore = useTaskStore()
const {push} = useRouter()
const {t} = useI18n()
const {replace} = useRouter()
const imageData = ref<string>('')
const headSrc = ref('')
const unreadMsgCount = ref({})
const wddbCount = ref(0)
const errorHandler = () => true
const handleCommand = (command) => {
  if (command === 'a') {
    // 打开个人中心
    push('/Portal/UserCenter/userInfo')
  } else if (command === 'b') {
    // 退出登录
    loginOut()
  }
}
const loginOut = async () => {
  try {
    await ElMessageBox.confirm(t('common.loginOutMessage'), t('common.reminder'), {
      confirmButtonText: t('common.ok'),
      cancelButtonText: t('common.cancel'),
      type: 'warning'
    })
    await userStore.loginOut()
    // replace('/login?redirect=/Portal/WorkCenter')
    replace('/portal/home/<USER>')
  } catch {}
}
watch(
  () => taskStore.getTaskCount,
  (newValue) => {
    wddbCount.value = newValue
  },
  {
    immediate: true
  }
)
onMounted(async () => {
  unreadMsgCount.value = await PortalApi.getMsgCount()
  getImage()
  getDBCount()
})
const getImage = () => {
  if (isBinaryData(userStore.getUser.avatar)) {
    let type = userStore.getUser.avatar.replace(/^0x/, '')
    let bytes = []
    for (let i = 0; i < type.length; i += 2) {
      bytes.push(parseInt(type.substr(i, 2), 16))
    }
    imageData.value = btoa(String.fromCharCode.apply(null, bytes))
    headSrc.value = 'data:image/jpg;base64,' + imageData
  } else {
    headSrc.value = import.meta.env.VITE_TOURL_PREFIX + '/BasicApplication/DownloadFile?FileID=' + userStore.getUser.avatar + '&token=' + getAccessToken()
  }
}

async function getDBCount() {
  await taskStore.initNewTaskList({ page: 1, pageSize: 1 })
  wddbCount.value = taskStore.getTaskCount
}

function isBinaryData(data) {
  // 检查是否是ArrayBuffer或Blob类型
  if (data instanceof ArrayBuffer || data instanceof Blob) {
    return true
  }

  // 检查是否是base64编码的图片
  if (typeof data === 'string' && data.startsWith('data:image/')) {
    return true
  }

  // 其他情况视为图片名称
  return false
}

const openMsgPage = () => {
  let url =
    import.meta.env.VITE_TOURL_PREFIX +
    '/BasicApplication/InfoManage/Msg/MsgTab?token=' +
    getAccessToken()
  const obgdata = {
    pagename: '我的消息',
    pageurl: url,
    tag: '头部菜单'
  }
  LogApi.pageLog(obgdata)
  window.open(url)
}
const openTodo = () => {
  const obgdata = {
    pagename: '我的待办',
    pageurl: '/Portal/WorkCenter/MyWork',
    tag: '头部菜单'
  }
  LogApi.pageLog(obgdata)
  useAppStore().set_work_menu('newtask') //设置全局变量
  push({ path: '/Portal/WorkCenter/MyWork', query: { code: 'newtask' } }) //跳转到我的代办
}
</script>
<style scoped lang="scss">
.right-side {
  display: flex;
  list-style: none;

  li {
    display: flex;
    align-items: center;
    padding: 0 16px;

    .message-box-trigger {
      cursor: pointer;
      height: 26px;

      .icon {
        width: 26px;
        height: 26px;
      }
    }

    .el-dropdown-link {
      display: flex;
      cursor: pointer;
      color: #fff;
      align-items: center;
      gap: 12px;
    }

    .el-dropdown-link:focus-visible {
      outline: none;
      border: none;
      box-shadow: none;
    }
  }
}

.name {
  font-size: 18px;
}

:deep(.el-badge__content) {
  border: 0 !important;
}
</style>
