<template>
  <div class="bottomers">
    <system-help/>
    <div class="bottomer">
      <div class="logo">
        <img src="@/assets/icons/portal/logo2.png" class="icon"/>
      </div>
      <div class="bottomer-inner">
        <div>Copyright © 版权所有 中国电建集团昆明勘测设计研究院有限公司</div>
        <br/>
        <div>技术支持：信息技术研究院 数字信息中心</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import $ from 'jquery'
import * as portal from '@/api/portal'
import * as PortalApi from '@/api/system/portal'
import systemHelp from "./system_help.vue"
// 访问前缀
const baseUrl = import.meta.env.VITE_TOURL_PREFIX
import {getAccessToken} from '@/utils/auth';

const {push} = useRouter();

const props = defineProps({
  label: {
    type: String,
    required: false
  }
});

const lxyw = ref<boolean>(false);
const sideShow = ref<boolean>(false);
const closeOpen = () => {
  sideShow.value = !sideShow.value
}
//联系运维
const lxywOpen= async () => {
  lxyw.value = !lxyw.value;
}
// 展示意见反馈
const show_yjfk = async () => {
  // 获取guid
  const ret = await portal.getGuid();
  const url = baseUrl + "/UIBuilder/UIViewer/FormViewer?TempletCode=Page_ad6b01070e794366ac11727ad66a0014&ID=" + ret + "&token=" + getAccessToken()
  console.log(url)
  openWindow(url, "意见反馈", 1346, 600)
}

// 温馨提示列表
const wxtsList = ref()

const currentId = ref<number>(1)
const showBackTop = ref<Boolean>(false)

const nowItem = ref({
  id: '',
  title: '',
  img: ''
})
const wxts_list = async () => {
  currentId.value = 1
  // 获取guid
  const ret = await PortalApi.GetWarmPromptList({"page": 1, "pageSize": 50});
  wxtsList.value = ret.records
  //将第一个给
  getNextObject()
}

// 打开窗口
const openWindow = (url: string, title: string, width: number, height: number) => {
  // 获取屏幕的宽度和高度
  const screenWidth = screen.width;
  const screenHeight = screen.height;
  // 计算窗口的x和y坐标以使其居中
  const x = (screenWidth - width) / 2;
  const y = (screenHeight - height) / 2;
  const newWindow = window.open(
    url,
    title,
    `width=${width},height=${height},left=${x},top=${y}`
  );
  if (newWindow && newWindow.focus) {
    newWindow.focus();
  }
  return newWindow;
};

// 循环获取对象数据
const getNextObject = () => {
  if (wxtsList.value.length < currentId.value) {
    currentId.value = 1;
  }
  let nextObj = wxtsList.value.find(obj => obj.RID == currentId.value);
  currentId.value += 1;

  nowItem.value.title = nextObj.Title
  nowItem.value.img = nextObj.PhotoAddress
  nowItem.value.id = nextObj.ID
}



//打开温馨提示对话框
function show_wxts_dialog() {
  wxts_list();
  //打开半透明遮照
  $("#wxts_dialog_mask").fadeIn();

  //隐藏温馨提示 - 弹出框
  $("#wxts_dialog").show();
  $("#wxts_dialog").removeClass("wxts_dialog_close");

  //显示温馨提示 - 侧边栏
  $("#wxts_slidbar").fadeOut();
}

//关闭温馨提示
function close_wxts_btn() {
  //隐藏半透明遮照
  $("#wxts_dialog_mask").fadeOut();

  //隐藏温馨提示 - 弹出框
  $("#wxts_dialog").addClass("wxts_dialog_close");

  //显示温馨提示 - 侧边栏
  setTimeout(function () {
    $("#wxts_slidbar").fadeIn();
  }, 600);

  //数据库存储温馨提示
  // this.save_wxts_data(0);
}

// 查看详情
const toWarmDetail = () => {
  // push({ path: '/Portal/WarmPromptDetail', query: { "id": props.warmItem.ID } })
  window.open("/Portal/WarmPromptDetail?id=" + nowItem.value.id)
}


//保存温馨提示点击
// function save_wxts_data(open_status) {
//   //数据库存储温馨提示
//   execSystemScript({
//     code: "SQL_adf300ecfccb41d696c16bed06ab8890",
//     async: false,
//     showLoading: false,
//     data: {
//       ExecData: "[" + mini.encode({ USERID: _UserInfo.ID, SHOW: open_status, COUNT: this.wxts_list.length }) + "]"
//     },
//     callBack: function (status, data) { }
//   });
// }
const toWarmpList = () => {
  push("/Portal/WarmPromptList")

  close_wxts_btn();
}
//打开帮助文档
let title = ref("");
const full = ref(false);
let openDialog = ref(false);
const docHeight = ref(450);

function updateTitle(newTitle: string) {
  title.value = newTitle;
}

function openHelp() {
  openDialog.value = true;
  // console.log("打开帮助文档");
  // //从数据库，判断温馨提示是否要展示
  // execSystemScript({
  //   code: "SQL_adf401319a5d40378dd5e13b17cc36f2",
  //   async: false,
  //   showLoading: false,
  //   data: {
  //     ExecData: "[" + mini.encode({ USERID: _UserInfo.ID }) + "]"
  //   },
  //   callBack: function (status, data) {
  //     if (data.SQL_adf401319a5d40378dd5e13b17cc36f2.length > 0) {
  //       window.open("/BasicApplication/DownloadFile?FileID=" + data.SQL_adf401319a5d40378dd5e13b17cc36f2[0].WJ);
  //     }
  //   }
  // });
}

const openFull = () => {
  full.value ? (full.value = false) : (full.value = true);
  if (full.value) {
    docHeight.value = 680
  } else {
    docHeight.value = 450
  }

}


</script>

<style lang="scss" scoped>
@import url('@/assets/css/master.css');

* {
  font-family: "Microsoft YaHei", "MicrosoftYaHei";
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}



.avue-crud_dialog_menu {
  margin-right: 3px;
}

.avue-crud_dialog_header {
  display: grid;
  grid-template-columns: 1fr auto;
  justify-content: center;
  align-items: center;
}

/**底部 */
.bottomer {
  box-shadow: 0 0 6px 0 rgb(7 0 2 / 5%);
  //background: #3d3e4a;
  padding: 30px 0 15px 0;
  position: relative;
  z-index: 2;
  background-image: url("@/assets/icons/portal/bottomer.png");
  background-size: 100% 100%;

  .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-bottom: 20px;
    background: url('@/assets/icons/portal/footer_border.png') no-repeat bottom center;
    background-size: auto 2px;

    .icon {
      height: auto;
      width: 700px;
      padding-right: 20px;
    }

    img {
      vertical-align: middle;
      border: 0;
    }
  }

  .bottomer-inner {
    padding-top: 15px;
    width: 800px;
    text-align: center;
    margin: 0 auto;

    div {
      display: inline-block;
      font-size: 14px;
      color: #6b6c7d;
    }
  }
}

.hbm-ad-docker {
  height: 227px;
  width: 120px;
  position: fixed;
  top: 420px;
  background-color: transparent;
  z-index: 999998;
  right: 10px;
  bottom: 100px;

  .hbm-ad-docker-panel {
    border: 1px solid #e5e5e5;
    background-color: #ecf2fc;

    &:last-child {
      margin-top: 10px;
    }

    .hbm-ad-docker-item {
      width: 70px;
      height: 72px;
      text-align: center;
      padding-top: 13px;
      cursor: pointer;

      span {
        display: block;
        height: 36px;
        line-height: 36px;
        color: inherit;
        font-size: 14px;
      }

      .hbm-ad-yw {
        background: url('@/assets/icons/portal/yuanoaicos.png') no-repeat 0 -92px;
      }

      .hbm-ad-fk {
        background: url('@/assets/icons/portal/yuanoaicos.png') no-repeat -24px -92px;
      }

      .hbm-ad-img {
        width: 23px;
        height: 21px;
        margin: 0 auto;
      }

      .hbm-ad-backtop {
        background: url('@/assets/icons/portal/yuanoaicos.png') no-repeat 0px -116px;
        width: 14px;
        height: 9px;
        display: inline-block;
        margin-top: 18px;
      }

      .hbm-ad-backtop-title {
        display: none;
        line-height: 25px;
      }

      &:hover {
        color: #0070ff;

        .hbm-ad-yw {
          background: url('@/assets/icons/portal/yuanoaicos.png') no-repeat -69px -92px;
        }

        .hbm-ad-fk {
          background: url('@/assets/icons/portal/yuanoaicos.png') no-repeat -92px -92px;;
        }
      }
    }

    .hbm-ad-docker-item[atr="totop"]:hover {
      .hbm-ad-backtop-title {
        display: inline-block;
        color: #eee;
        width: 40px;
      }

      .hbm-ad-backtop {
        display: none;
      }
    }
  }

  #topanchor {
    background-color: #b7cae9;
  }
}

.hbm-ad-docker-ywlist {
  height: 403px;
  width: 160px;
  position: fixed;
  // right: 20px;
  // top: 580px;
  background-color: #ffffff;
  border: 1px solid #e5e5e5;
  border-radius: 0.25rem;
  box-shadow: 0 0 6px 0 rgba(7, 0, 2, 0.05);
  padding-top: 13px;
  display: none;
  z-index: 999998;
  right: 85px !important;
  left: auto !important;
  top: 169px !important;

  .hbm-ad-docker-yw-item {
    width: 130px;
    margin: 6px auto;
    padding: 4px;

    span {
      display: inline-block;
      margin-right: 6px;
      vertical-align: middle;
    }

    .hbm-ad-docker-yw-row {
      height: auto;

      .hbm-yw-item-title {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        /* 将对象作为弹性伸缩盒子模型显示 */
        -webkit-line-clamp: 1;
        /* 控制最多显示几行 */
        -webkit-box-orient: vertical;
        /* 设置或检索伸缩盒对象的子元素的排列方式 */
      }
    }

    .hbm-yw-item-title {
      font-size: 12px;
      font-weight: bold;
      color: #404a56;
    }

    .hbm-yw-item-userico {
      width: 12px;
      height: 12px;
      background: url('@/assets/icons/portal/yuanoaicos.png') no-repeat -44px 0;
    }

    .hbm-yw-item-username {
      font-size: 12px;
      font-weight: bold;
      color: #999999;
    }

    .hbm-yw-item-tel {
      font-size: 12px;
      font-weight: 500;
      color: #999999;
    }

    .hbm-yw-item-telico {
      width: 11px;
      height: 11px;
      background: url('@/assets/icons/portal/yuanoaicos.png') no-repeat -72px 0;
    }

    .hbm-yw-item-msgtouserico {
      width: 16px;
      height: 14px;
      background: url('@/assets/icons/portal/yuanoaicos.png') no-repeat -56px 0;
      cursor: pointer;
    }
  }
}

#wxts_slidbar {
  position: fixed;
  right: 10px;
  top: 240px;
  width: 72px;
  height: 72px;
  border: 1px solid #ddd;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  background: #fff;
  z-index: 999;

  .text {
    margin-top: auto;
    margin-bottom: 10px;
    font-size: 14px;
  }
}

#help_slidbar {
  position: fixed;
  right: 10px;
  top: 320px;
  width: 72px;
  height: 72px;
  border: 1px solid #ddd;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  background: #fff;
  z-index: 99;

  .text {
    margin-top: auto;
    margin-bottom: 10px;
    font-size: 14px;
  }
}

/*--------- 温馨提示 start ---------*/
.wxts_dialog {
  background: #fff;
  position: fixed;
  bottom: 50%;
  right: 50%;
  margin-right: -200px;
  margin-bottom: -175px;
  z-index: 9999999;
  width: 400px;
  height: 350px;
  border-radius: 10px !important;
  transition: all 0.8s;
  overflow: hidden;
  display: none;
}

.wxts_dialog_close {
  position: fixed;
  right: 10px;
  bottom: 440px;
  width: 0px;
  height: 0px;
  margin-right: 0px;
  margin-bottom: 0px;
  overflow: hidden;
  cursor: pointer;
}

.wxts_dialog .uk-card-header {
  padding: 0;
  margin: 0;
  height: 50px;
  padding: 0 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.wxts_dialog .refresh {
  margin-left: 10px;
  cursor: pointer;
}

.el-icon-refresh {
  margin-top: 5px
}

.wxts_dialog .more {
  margin-right: auto;
  margin-left: 232px;
  cursor: pointer;
}

.wxts_dialog .close {
  margin-left: auto;
  width: 26px;
  height: 26px;
  border-radius: 30px;
  line-height: 20px;
  border: 1px solid rgb(201, 201, 201);
  font-size: 24px;
  text-align: center;
  transform: rotate(45deg);
  cursor: pointer;
}

.wxts_dialog .has_tip {
  position: relative;
}

.wxts_dialog .tip {
  position: absolute;
  background: #000;
  opacity: 0.5;
  color: #fff;
  width: 110px;
  height: 27px;
  line-height: 30px;
  border-radius: 4px;
  text-align: center;
  left: -50px;
  top: 24px;
  display: none;
  transition: all 0.3s;
  z-index: 9999998;
  font-size: 14px;
}

.wxts_dialog .tip::after {
  content: "";
  width: 0;
  height: 0;
  position: absolute;
  top: -12px;
  left: 50px;
  border-width: 7px;
  border-style: solid;
  border-color: transparent transparent #000 transparent;
}

.wxts_dialog .has_tip:hover .tip {
  display: block;
}

/*温馨提示 - 半透明遮照*/
#wxts_dialog_mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999998;
  display: none;
}

/*温馨提示 - 侧边栏*/
#wxts_slidbar {
  position: fixed;
  right: 10px;
  bottom: 419px;
  width: 72px;
  height: 72px;
  border: 1px solid #ddd;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  background: #fff;
}

#wxts_slidbar .text {
  margin-top: auto;
  margin-bottom: 10px;
}

// #wxts_slidbar .icon {
//     width: 26px;
//     height: 26px;
//     margin-bottom: 4px;
//     background: url("../images/wxts_icon.png") no-repeat center center;
//     background-size: cover;
//     margin-bottom: auto;
//     margin-top: 10px;
//     transition: all 0.3s;
// }

// #wxts_slidbar:hover .icon {
//     background: url("../images/wxts_icon_active.png") no-repeat center center;
//     background-size: cover;
//     animation: bounce 1s ease-in-out;
// }

#wxts_slidbar:hover .text {
  color: #0070ff;
}

@keyframes bounce {

  0%,
  20%,
  53%,
  80%,
  100% {
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translate3d(0, 0, 0);
  }

  40%,
  43% {
    transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -13px, 0);
  }

  70% {
    transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -8px, 0);
  }

  90% {
    transform: translate3d(0, -2px, 0);
  }
}

.page-work .uk-card-body {
  padding: 0;
}

.el-carousel__container {
  position: relative;
  height: 300px;
}

.warm-prompt-box-item {
  padding: 8px 14px;
}

.warm-prompt-box-item:hover {
  cursor: pointer;
}

.warm-prompt-box-title {
  font-size: 14px;
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  /* 将对象作为弹性伸缩盒子模型显示 */
  -webkit-line-clamp: 1;
  /* 控制最多显示几行 */
  -webkit-box-orient: vertical;
  /* 设置或检索伸缩盒对象的子元素的排列方式 */
}

.warm-prompt-box-img {
  width: 100%;
  height: 220px;
  /*180px*/
  margin: 5px 0;
}

.warm-prompt-box-img1 {
  width: 100%;
  height: 220px;
  /*260px*/
  margin: 5px 0;
}

.warm-prompt-box-content {
  width: 100%;
  max-height: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  /* 将对象作为弹性伸缩盒子模型显示 */
  -webkit-line-clamp: 4;
  /* 控制最多显示几行 */
  -webkit-box-orient: vertical;
  /* 设置或检索伸缩盒对象的子元素的排列方式 */
}

.warm-prompt-more-label {
  cursor: pointer;
  text-decoration: none;
  color: #333;
}

.warm-prompt-more-label:hover {
  text-decoration: none;
  color: #333;
}

/*--------- 温馨提示 end ---------*/
</style>
