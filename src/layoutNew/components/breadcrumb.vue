<script setup lang="ts">
import {string} from "vue-types";

const props = defineProps({
  title: {
    type: string,
    required: false,
    default: ''
  },
  activeName: {
    type: string,
    required: false,
    default: ''
  }
})
</script>

<template>
  <div class="position">
    <div class="center">
      <div class="href">
        <div class="h">首页</div>
        <div class="l">&gt;</div>
        <div class="h">{{title}}</div>
        <div v-if="activeName" class="l">&gt;</div>
        <div v-if="activeName" class="h">{{activeName}}</div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.position {
  height: 46px;
  margin-top: 12px;
  display: none;
}

.center {
  width: 1252px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
}

.position .center .href {
  width: 1237px;
  height: 46px;
  margin: 0 auto;
  align-items: center;
  background: #f7f9ff;
  display: flex;
  color: #17273a;
  font-size: 14px;
  padding-left: 15px;
}

.position .center .href .h {
  cursor: pointer;
}

.position .center .href .l {
  margin: 0 5px;
}
</style>
