<template>
  <div class="head_banner">
    <div class="banner">
      <swiper
        class="swiper-container"
        :slides-per-view="1"
        :space-between="0"
        @swiper="onSwiper"
        @slide-change="onSlideChange"
        :modules="modules"
        :pagination="{ clickable: true }"
        :autoplay="{ delay: 10000, disableOnInteraction: false }"
        :loop="true">
        <swiper-slide v-for="(item, index) in homeImag" :key="index">
          <div class="image-container header-img">
            <img
              class="item"
              :src="visibleSlides.has(index) ? item.url : item.lowQualityUrl"
              :data-index="index"
              @load="imageLoaded(index)"
            />
          </div>
        </swiper-slide>
      </swiper>
    </div>
  </div>
</template>
<script lang="ts" setup>
import {ref, onMounted, onBeforeUnmount, nextTick} from 'vue'
import {Swiper, SwiperSlide} from 'swiper/vue'
import {Pagination, Autoplay} from 'swiper/modules'
import {getZXZXHeaderImg} from '@/api/portal'
import 'swiper/css';
import 'swiper/css/navigation'
import 'swiper/css/pagination'
import 'swiper/css/autoplay'

const modules = [Pagination, Autoplay]
const swiperInstance = ref<typeof Swiper | null>(null)
const homeImag = ref<Array<any> | null>([])
const loading = ref(true)
const loadedImages = ref<Set<number>>(new Set())
const imageObserverHeader = ref<IntersectionObserver | null>(null)
const visibleSlides = ref<Set<number>>(new Set([0])) // 默认第一张是可见的

const onSwiper = (swiper: typeof Swiper) => {
  swiperInstance.value = swiper
  // 初始化时预加载第一张图片
  if (homeImag.value && homeImag.value.length > 0) {
    preloadImage(homeImag.value[0].url)
  }
}

const onSlideChange = () => {
  // 获取当前活动的幻灯片索引
  const currentIndex = swiperInstance.value?.activeIndex
  if (currentIndex !== undefined && homeImag.value) {
    // 标记当前幻灯片为可见
    visibleSlides.value.add(currentIndex)

    // 预加载下一张和前一张图片
    const nextIndex = (currentIndex + 1) % (homeImag.value.length || 1)
    const prevIndex = (currentIndex - 1 + homeImag.value.length) % homeImag.value.length

    if (homeImag.value[nextIndex]) {
      preloadImage(homeImag.value[nextIndex].url)
      visibleSlides.value.add(nextIndex)
    }

    if (homeImag.value[prevIndex]) {
      preloadImage(homeImag.value[prevIndex].url)
      visibleSlides.value.add(prevIndex)
    }
  }
}

const preloadImage = (url: string) => {
  if (!url) return

  // 使用Promise来跟踪图片加载
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => resolve(img)
    img.onerror = reject
    img.src = url
  })
}

const imageLoaded = (index: number) => {
  loadedImages.value.add(index)
  if (homeImag.value && loadedImages.value.size === homeImag.value.length) {
    loading.value = false
  }

  // 当图片加载完成时，添加淡入效果
  if (homeImag.value && homeImag.value[index]) {
    homeImag.value[index].loaded = true
  }
}

// 设置图片观察器，实现更智能的懒加载
const setupImageObserver = () => {
  if ('IntersectionObserver' in window) {
    imageObserverHeader.value = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement
          const index = Number(img.dataset.index || '0')

          // 如果图片进入视口，预加载它
          if (homeImag.value && homeImag.value[index]) {
            const url = homeImag.value[index].url
            if (url && !loadedImages.value.has(index)) {
              img.src = url
            }
          }

          imageObserverHeader.value?.unobserve(img)
        }
      })
    }, {
      rootMargin: '0px 0px 200px 0px', // 提前200px加载
      threshold: 0.1 // 当10%的图片可见时触发
    })
  }
}

onMounted(async () => {
  setupImageObserver()
  await getSlideImgs()

  // 在图片加载后，观察所有图片元素
  nextTick(() => {
    const box = document.querySelectorAll('.header-img img')
    box.forEach((img, index) => {
      if (imageObserverHeader.value) {
        imageObserverHeader.value.observe(img)
      }
    })
  })
})

onBeforeUnmount(() => {
  // 清理观察器
  if (imageObserverHeader.value) {
    imageObserverHeader.value.disconnect()
  }
})

const getSlideImgs = async () => {
  loading.value = true
  homeImag.value = []
  try {
    const res = await getZXZXHeaderImg()
    if (!res) return
    for (let i = 0; i < res.length; i++) {
      const imageUrl = import.meta.env.VITE_TOURL_PREFIX + '/BasicApplication/DownloadFile/IndexAnonymous?FileID=' + res[i].img
      if (homeImag.value) {
        homeImag.value.push({
          url: imageUrl,
          lowQualityUrl: imageUrl,
          loaded: false
        })

        // 预加载第一张图片
        if (i === 0) {
          preloadImage(imageUrl)
        }
      }
    }
  } finally {
    // 如果没有图片或加载失败，也要关闭加载状态
    if (!homeImag.value || homeImag.value.length === 0) {
      loading.value = false
    }
  }
}
</script>

<style scoped lang="scss">
.head_banner {
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 1;
  background: linear-gradient(to bottom, #000000, #015771);

  .banner {
    opacity: 1;
    height: 100%;
    width: 100%;

    :deep(.swiper) {
      width: 100%;
      height: 100%;
    }
  }
}

.swiper-pagination-bullets .swiper-pagination-bullet {
  width: 10px;
  height: 10px;
  background: #fff;
  opacity: 0.5;
}

.head_banner :deep(.swiper-pagination-bullets) {
  margin-bottom: 30px;
}

.head_banner :deep(.swiper-pagination-bullets .swiper-pagination-bullet) {
  width: 10px;
  height: 10px;
  background: #fff;
  opacity: 0.5;
}

.head_banner :deep(.swiper-pagination-bullets .swiper-pagination-bullet-active) {
  opacity: 1;
}

.item {
  width: 100%;
  height: 450px;
  object-fit: cover; /* 保持图片比例并填充容器 */
  transition: opacity 0.5s ease; /* 平滑过渡效果 */
  //opacity: 0.6;
  //filter: blur(2px);

  &.loaded {
    opacity: 1;
    filter: blur(0);
  }
}

.image-container {
  position: relative;
  width: 100%;
  height: 450px;
  background-color: transparent; /* 占位背景色 */
  overflow: hidden;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.1);
  z-index: 2;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
