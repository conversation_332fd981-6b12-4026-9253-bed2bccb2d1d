<template>
  <div>
    <div
      class="uk-card uk-card-default work-card wxts_dialog"
      :class="[showDialog? '' : 'wxts_dialog_close']">
      <div class="uk-flex uk-card-header">
        <span class="uk-card-title">温馨提示</span>
        <span class="refresh has_tip">
            <img class="el-icon-refresh" src="@/assets/imgs/refresh.png" @click="getNextObject"
                 style="width:16px;height:16px;"/>
            <div class="tip">点击加载下一条</div>
          </span>
        <a class="more has_tip">
          <img src="@/assets/icons/portal/icon-more.png" @click="toWarmpList"/>
          <div class="tip">点击获取更多</div>
        </a>
        <div class="close" @click="closeDialog()">+</div>
      </div>
      <div class="uk-card-body cygn-body">
        <div class="el-carousel el-carousel--horizontal">
          <div class="el-carousel__container" @click="toWarmDetail">
            <div class="el-carousel__item warm-prompt-box-item is-active is-animating"
                 style="transform: translateX(0px) scale(1);">
              <div>
                <img :src="getImg(nowItem.img)" class="warm-prompt-box-img"/>
                <div class="warm-prompt-box-content" style="margin: 10px 0px;">
                  <div class="warm-prompt-box-title">{{ nowItem.title }}</div>
                  <span
                    style="font-size: 12px;">请点击查看详情...</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <transition name="fade">
      <div v-if="showDialog" class="dialog_mask" @click="closeDialog()"></div>
    </transition>
  </div>
</template>
<script setup lang="ts">
const emit = defineEmits(['closeDialog'])
import * as PortalApi from "@/api/system/portal";

const {push} = useRouter();
const baseUrl = import.meta.env.VITE_TOURL_PREFIX
// 温馨提示列表
const wxtsList = ref()
const currentId = ref<number>(1)
const nowItem = ref({
  id: '',
  title: '',
  img: ''
})
defineProps({
  showDialog: {
    type: Boolean,
    default: false
  }
});
const getImg = (img: any) => {
  if (img == '') {
    return;
  }
  return baseUrl + "/BasicApplication/DownloadFile?FileID=" + img;
}
onMounted(() => {
  getWxtsList()
})
const getWxtsList = async () => {
  currentId.value = 1
  // 获取guid
  const ret = await PortalApi.GetWarmPromptList({"page": 1, "pageSize": 50});
  wxtsList.value = ret.records
  //将第一个给
  getNextObject()
}
const getNextObject = () => {
  if (wxtsList.value.length < currentId.value) {
    currentId.value = 1;
  }
  let nextObj = wxtsList.value.find(obj => obj.RID == currentId.value);
  currentId.value += 1;

  nowItem.value.title = nextObj.Title
  nowItem.value.img = nextObj.PhotoAddress
  nowItem.value.id = nextObj.ID
}
const toWarmpList = () => {
  push("/Portal/WarmPromptList")
  closeDialog()
}
const toWarmDetail = () => {
  window.open("/Portal/WarmPromptDetail?id=" + nowItem.value.id)
}
const closeDialog = () => {
  emit('closeDialog', false)
}
</script>
<style scoped lang="scss">
@import "@/assets/css/uikit.min.css";
@import "@/assets/css/bootstrap.min.css";

.uk-card-body {
  padding: 0;
}

.dialog_mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(30px);
  }
}

/* 淡出动画 */
.fade-height-leave-active {
  transition: all 0.5s ease;
  /* 初始高度设置为auto */
  height: auto;
  overflow: hidden;
}

.fade-height-leave-to {
  opacity: 0;
  /* 结束高度设置为0 */
  height: 0 !important;
  /* 移除内边距和外边距使效果更平滑 */
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

/*--------- 温馨提示 start ---------*/
.wxts_dialog {
  position: fixed;
  bottom: 50%;
  right: 50%;
  margin-right: -200px;
  margin-bottom: -175px;
  z-index: 9999999;
  width: 400px;
  height: 350px;
  border-radius: 10px !important;
  transition: all 0.8s;
  overflow: hidden;
}

.wxts_dialog .uk-card-header {
  margin: 0;
  height: 50px;
  padding: 0 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .uk-card-title {
    font-size: 14px;
    line-height: 1.4;
  }
}

.wxts_dialog .refresh {
  margin-left: 10px;
  cursor: pointer;
}

.wxts_dialog .refresh:hover .el-icon-refresh {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(359deg);
  }
}

.wxts_dialog_close {
  position: fixed;
  right: 10px;
  bottom: 440px;
  width: 0px;
  height: 0px;
  margin-right: 0px;
  margin-bottom: 0px;
  overflow: hidden;
  cursor: pointer;
}

.wxts_dialog .more {
  margin-right: auto;
  margin-left: 232px;
}

.wxts_dialog .close {
  margin-left: auto;
  width: 26px;
  height: 26px;
  border-radius: 30px;
  line-height: 20px;
  border: 1px solid rgb(201, 201, 201);
  font-size: 24px;
  text-align: center;
  transform: rotate(45deg);
}

.wxts_dialog .has_tip {
  position: relative;
}

.wxts_dialog .tip {
  position: absolute;
  background: #000;
  opacity: 0.5;
  color: #fff;
  width: 120px;
  height: 30px;
  line-height: 30px;
  border-radius: 4px;
  text-align: center;
  left: -50px;
  top: 24px;
  display: none;
  transition: all 0.3s;
  z-index: 9999998;
}

.wxts_dialog .tip::after {
  content: "";
  width: 0;
  height: 0;
  position: absolute;
  top: -12px;
  left: 50px;
  border-width: 7px;
  border-style: solid;
  border-color: transparent transparent #000 transparent;
}

.wxts_dialog .has_tip:hover .tip {
  display: block;
}

.warm-prompt-box-item {
  padding: 8px 14px;
  cursor: pointer;
}

.warm-prompt-box-img {
  width: 100%;
  height: 220px;
  margin: 5px 0;
}

.warm-prompt-box-title {
  font-size: 14px;
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
</style>
