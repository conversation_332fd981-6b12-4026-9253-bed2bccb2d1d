export default {
  path: '/alone',
  name: 'alone',
  meta: {},
  children: [
    {
      path: 'database/addTenantId',
      name: 'addTenantId',
      component: () => import('@/views/system/tenantIdConfig/index.vue'),
      meta: {
        hidden: true,
        title: '数据库添加租户id'
      },
    },
    {
      path: 'oauth2/tokenConfig',
      name: 'tokenConfig',
      component: () => import('@/views/system/oauth2/client/index.vue'),
      meta: {
        hidden: true,
        title: '应用管理'
      },
    },
    {
      path: 'oauth2/token',
      name: 'token',
      component: () => import('@/views/system/oauth2/token/index.vue'),
      meta: {
        hidden: true,
        title: '令牌管理'
      },
    },
    {
      path: 'disk/diskConfig',
      name: 'diskConfig',
      component: () => import('@/views/infra/diskFileConfig/index.vue'),
      meta: {
        hidden: true,
        title: '磁盘处理'
      },
    },
    {
      path: 'disk/diskfile',
      name: 'diskfile',
      component: () => import('@/views/infra/diskFile/index.vue'),
      meta: {
        hidden: true,
        title: '文件处理'
      },
    },
    // {
    //   path: '/alone/layout',
    //   name: 'layout',
    //   component: () => import('@/layout/portal/ServiceLeftNav.vue'),
    //   meta: {
    //     hidden: true,
    //     title: 'layout'
    //   },
    // },
    {
      path: 'system/custom',
      name: 'custom',
      component: () => import('@/views/portal/gridLayout/Custom.vue'),
      meta: {
        hidden: true,
        title: 'custom'
      },
    },
    {
      path: 'system/handle',
      name: 'handle',
      component: () => import('@/views/portal/gridLayout/HandleLayout.vue'),
      meta: {
        hidden: true,
        title: 'handle'
      },
    },
    {
      path: 'infra/job',
      name: 'job',
      component: () => import('@/views/infra/job/index.vue'),
      meta: {
        hidden: true,
        title: 'job'
      },
    },
    {
      path: 'tenant/list',
      name: 'tenant',
      component: () => import('@/views/system/tenant/index.vue'),
      meta: {
        hidden: true,
        title: '租户管理'
      },
    },
    {
      path: 'tenantIdConfig/index',
      name: 'tenantIdConfig',
      component: () => import('@/views/system/tenantIdConfig/index.vue'),
      meta: {
        hidden: true,
        title: '租户ID字段管理'
      },
    },
    {
      path: 'codegen',
      name: 'codegen',
      component: () => import('@/views/infra/codegen/index.vue'),
      meta: {
        hidden: true,
        title: '代码生成'
      },
    },
    {
      path: 'build',
      name: 'build',
      component: () => import('@/views/infra/build/index.vue'),
      meta: {
        hidden: true,
        title: '表单生成'
      },
    },
    {
      path: 'sms/sms-channel',
      name: 'smsChannel',
      component: () => import('@/views/system/sms/channel/index.vue'),
      meta: {
        hidden: true,
        title: '短信渠道'
      },
    },
    {
      path: 'sms/sms-template',
      name: 'smsTemplate',
      component: () => import('@/views/system/sms/template/index.vue'),
      meta: {
        hidden: true,
        title: '短信模板'
      },
    },
    {
      path: 'sms/sms-log',
      name: 'smsLog',
      component: () => import('@/views/system/sms/log/index.vue'),
      meta: {
        hidden: true,
        title: '短信日志'
      },
    },
  ]
}
