// 引入unocss css
import '@/plugins/unocss'

// 导入全局的svg图标
import '@/plugins/svgIcon'

// 初始化多语言
import {setupI18n} from '@/plugins/vueI18n'

// 引入状态管理
import {setupStore} from '@/store'

// 全局组件
import {setupGlobCom} from '@/components'

// 引入 element-plus
import {setupElementPlus} from '@/plugins/elementPlus'

// 引入 form-create
import {setupFormCreate} from '@/plugins/formCreate'

// 引入 grid-layout
import VueGridLayout from 'vue-grid-layout'

//引入自定义富文本tool
import '@/plugins/document'

// 引入全局样式
import '@/styles/index.scss'

// 引入动画
import '@/plugins/animate.css'

// 路由
import router, {setupRouter} from '@/router'

// 权限
import {setupAuth} from '@/directives'

import {createApp} from 'vue'

import App from './App.vue'

import './permission'

import '@/plugins/tongji' // 百度统计
import Logger from '@/utils/Logger'

import VueDOMPurifyHTML from 'vue-dompurify-html' // 解决v-html 的安全隐患

import {ArrowRight} from '@element-plus/icons-vue'

// 创建实例
const setupAll = async () => {
  const app = createApp(App)

  await setupI18n(app)

  setupStore(app)

  setupGlobCom(app)

  setupElementPlus(app)

  app.component('ArrowRight', ArrowRight)

  setupFormCreate(app)

  setupRouter(app)

  setupAuth(app)

  // 恢复自动退出登录功能（仅在有有效token时）
  const { autoLogoutManager, getAccessToken } = await import('@/utils/auth')
  if (getAccessToken()) {
    autoLogoutManager.restore()
  }

  // 访问前缀
  const baseUrl = import.meta.env.VITE_TOURL_PREFIX

  //const baseUrl = 'http://10.10.1.35:8080/'
  app.config.globalProperties.$baseUrl = baseUrl
  app.config.globalProperties.$getFullUrl = (params) => {
    return baseUrl + params
  }

  await router.isReady()

  app.use(VueDOMPurifyHTML)

  app.use(VueGridLayout)

  app.mount('#app')
  app.directive('show-hide', {
    mounted(el, binding) {
      el.style.transition = 'all 0.5s ease';
      el.style.overflow = 'hidden';
      updateStyles(el, binding.value);
    },
    updated(el, binding) {
      updateStyles(el, binding.value);
    }
  });

  function updateStyles(el, value) {
    if (value) {
      el.style.height = el.scrollHeight + 'px';
      el.style.opacity = 1;
    } else {
      el.style.height = 0;
      el.style.opacity = 0;
    }
  }

}

setupAll()

Logger.prettyPrimary(`欢迎使用`, import.meta.env.VITE_APP_TITLE)
